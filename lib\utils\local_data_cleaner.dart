/// 로컬 데이터 완전 삭제 유틸리티
/// 
/// 로그아웃, 회원탈퇴 시 모든 로컬 데이터를 완전히 삭제하는 기능을 제공합니다.
library;

import 'dart:io';
import 'package:flutter/material.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';

import '../services/database_service.dart';
import '../providers/nickname_provider.dart';
import '../utils/logger_utils.dart';
import '../utils/image_cache.dart';
import '../utils/image_sync_utils.dart';


class LocalDataCleaner {
  static const String _tag = 'LocalDataCleaner';

  /// 모든 로컬 데이터를 완전히 삭제
  /// 
  /// [includeOnboarding] - 온보딩 상태도 삭제할지 여부 (기본값: true)
  /// [showProgress] - 진행 상황을 로그로 표시할지 여부 (기본값: true)
  static Future<void> clearAllLocalData({
    bool includeOnboarding = true,
    bool showProgress = true,
    WidgetRef? ref,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('🗑️ 모든 로컬 데이터 완전 삭제 시작', tag: _tag);
      }

      // 1. SharedPreferences 완전 삭제
      await _clearSharedPreferences(includeOnboarding: includeOnboarding, showProgress: showProgress);

      // 2. SQLite 데이터베이스 완전 삭제
      await _clearDatabase(ref: ref, showProgress: showProgress);

      // 3. 이미지 캐시 완전 삭제
      await _clearImageCache(showProgress: showProgress);

      // 4. 애플리케이션 문서 디렉토리의 모든 파일 삭제
      await _clearApplicationFiles(showProgress: showProgress);

      // 5. 임시 디렉토리 정리
      await _clearTemporaryFiles(showProgress: showProgress);

      // 6. Flutter 이미지 캐시 정리
      await _clearFlutterImageCache(showProgress: showProgress);

      if (showProgress) {
        LoggerUtils.logInfo('✅ 모든 로컬 데이터 완전 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('로컬 데이터 완전 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// SharedPreferences 완전 삭제
  static Future<void> _clearSharedPreferences({
    bool includeOnboarding = true,
    bool showProgress = true,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('1️⃣ SharedPreferences 삭제 중...', tag: _tag);
      }

      final prefs = await SharedPreferences.getInstance();
      
      if (includeOnboarding) {
        // 모든 데이터 삭제
        await prefs.clear();
      } else {
        // 온보딩 상태는 유지하고 나머지만 삭제
        final onboardingState = prefs.getBool('isOnboarded');
        await prefs.clear();
        if (onboardingState != null) {
          await prefs.setBool('isOnboarded', onboardingState);
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ SharedPreferences 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SharedPreferences 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// SQLite 데이터베이스 완전 삭제
  static Future<void> _clearDatabase({
    WidgetRef? ref,
    bool showProgress = true,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('2️⃣ SQLite 데이터베이스 삭제 중...', tag: _tag);
      }

      if (ref != null) {
        final databaseService = ref.read(databaseServiceProvider);
        await databaseService.forceMigration();
      } else {
        // ref가 없는 경우 직접 데이터베이스 파일 삭제 시도
        try {
          final databasesPath = await getDatabasesPath();
          final dbPath = '$databasesPath/parabara_database.db';
          final dbFile = File(dbPath);
          if (await dbFile.exists()) {
            await dbFile.delete();
          }
        } catch (e) {
          LoggerUtils.logWarning('데이터베이스 파일 직접 삭제 실패 (무시)', tag: _tag, error: e);
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ SQLite 데이터베이스 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SQLite 데이터베이스 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 이미지 캐시 완전 삭제
  static Future<void> _clearImageCache({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('3️⃣ 이미지 캐시 삭제 중...', tag: _tag);
      }

      // 커스텀 이미지 캐시 삭제
      await ImageCacheManager.clearAllLocalCache();
      await ImageSyncUtils.clearAllImageCache();

      if (showProgress) {
        LoggerUtils.logInfo('✅ 이미지 캐시 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('이미지 캐시 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 애플리케이션 문서 디렉토리의 모든 파일 삭제
  static Future<void> _clearApplicationFiles({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('4️⃣ 애플리케이션 파일 삭제 중...', tag: _tag);
      }

      final appDir = await getApplicationDocumentsDirectory();
      if (await appDir.exists()) {
        // 디렉토리 내용 삭제 (디렉토리 자체는 유지)
        final entities = appDir.listSync();
        for (final entity in entities) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            LoggerUtils.logWarning('파일/디렉토리 삭제 실패: ${entity.path}', tag: _tag, error: e);
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 애플리케이션 파일 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('애플리케이션 파일 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 임시 디렉토리 정리
  static Future<void> _clearTemporaryFiles({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('5️⃣ 임시 파일 삭제 중...', tag: _tag);
      }

      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        // 임시 디렉토리 내용 삭제
        final entities = tempDir.listSync();
        for (final entity in entities) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            LoggerUtils.logWarning('임시 파일/디렉토리 삭제 실패: ${entity.path}', tag: _tag, error: e);
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 임시 파일 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('임시 파일 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// Flutter 이미지 캐시 정리
  static Future<void> _clearFlutterImageCache({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('6️⃣ Flutter 이미지 캐시 삭제 중...', tag: _tag);
      }

      final imageCache = PaintingBinding.instance.imageCache;
      imageCache.clear();
      imageCache.clearLiveImages();

      if (showProgress) {
        LoggerUtils.logInfo('✅ Flutter 이미지 캐시 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('Flutter 이미지 캐시 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 로그아웃용 로컬 데이터 삭제 (온보딩 상태 유지)
  static Future<void> clearDataForLogout({WidgetRef? ref}) async {
    // 추가: 닉네임 Provider 상태도 초기화
    if (ref != null) {
      try {
        LoggerUtils.logInfo('닉네임 Provider 상태 초기화', tag: _tag);
        ref.read(nicknameProvider.notifier).clearNickname();
      } catch (e) {
        LoggerUtils.logWarning('닉네임 Provider 초기화 실패', tag: _tag, error: e);
      }
    }
    
    await clearAllLocalData(
      includeOnboarding: false,
      showProgress: true,
      ref: ref,
    );
  }

  /// 회원탈퇴용 로컬 데이터 삭제 (모든 데이터 삭제)
  static Future<void> clearDataForAccountDeletion({WidgetRef? ref}) async {
    // Provider 상태 먼저 안전하게 정리
    if (ref != null) {
      try {
        LoggerUtils.logInfo('회원탈퇴: Provider 상태 정리 시작', tag: _tag);
        
        // 닉네임 Provider 정리
        ref.read(nicknameProvider.notifier).clearNickname();
        
        // 잠시 대기하여 Provider 상태 정리가 완료되도록 함
        await Future.delayed(const Duration(milliseconds: 100));
        
        LoggerUtils.logInfo('회원탈퇴: Provider 상태 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logWarning('Provider 상태 정리 중 오류 (계속 진행): $e', tag: _tag);
      }
    }
    
    await clearAllLocalData(
      includeOnboarding: true,
      showProgress: true,
      ref: ref,
    );
  }

  /// 특정 사용자의 데이터만 삭제 (프로필 이미지 등)
  static Future<void> clearUserSpecificData(String userId, {bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('🗑️ 사용자별 데이터 삭제 시작: $userId', tag: _tag);
      }

      // 프로필 이미지 파일 삭제
      final appDir = await getApplicationDocumentsDirectory();
      final entities = appDir.listSync();
      
      for (final entity in entities) {
        if (entity is File) {
          final fileName = entity.path.split('/').last;
          if (fileName.contains('profile_image_${userId}_') || 
              fileName.contains('profile_${userId}_') ||
              fileName == 'profile_image_$userId.jpg') {
            try {
              await entity.delete();
              if (showProgress) {
                LoggerUtils.logInfo('사용자 파일 삭제: $fileName', tag: _tag);
              }
            } catch (e) {
              LoggerUtils.logWarning('사용자 파일 삭제 실패: $fileName', tag: _tag, error: e);
            }
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 사용자별 데이터 삭제 완료: $userId', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('사용자별 데이터 삭제 실패', tag: _tag, error: e);
    }
  }
}
