import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../../models/prepayment_sort_order.dart';
import '../../models/product_sort_option.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_state.dart';
import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../models/event_workspace.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../providers/settings_provider.dart';
import '../../utils/app_animations.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/error_utils.dart';
import '../../utils/logger_utils.dart';
import '../../main.dart';
import '../prepayment/register_prepayment_screen.dart';
import '../product/register_product_screen.dart';
import 'inventory_tab.dart';
import 'prepayment_tab.dart';
import '../sale/sale_screen.dart';
import '../service/service_screen.dart';
import '../sales_log/sales_history_screen.dart';
import '../statistics/statistics_screen.dart';
import '../settings/settings_screen.dart';
import '../set_discount/set_discount_screen.dart';
import '../excel/excel_import_screen.dart';
import 'qr_scan_screen.dart';
import '../../widgets/excel_import_mode_dialog.dart';
import '../../providers/realtime_sync_provider.dart';
import '../../providers/excel_import_mode_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../providers/nickname_provider.dart';
import '../../widgets/profile_avatar_widget.dart';
import '../seller/seller_management_screen.dart';
import '../prepayment/prepayment_virtual_product_management_screen.dart';
import '../prepayment/prepayment_product_link_screen.dart';
import '../event/event_list_screen.dart';
import '../category/category_management_screen.dart';

class InventoryScreen extends ConsumerStatefulWidget {
  const InventoryScreen({super.key});

  @override
  ConsumerState<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends ConsumerState<InventoryScreen>
    with TickerProviderStateMixin, RestorationMixin {
  static const String _tag = 'InventoryScreen';
  late TabController _tabController;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // 현재 탭 인덱스 (0: 재고현황, 1: 선입금목록)
  int _currentTabIndex = 0;

  // 검색 모드 관련 변수들
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  String? get restorationId => 'inventory_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);

    // 모든 방향 허용 (인벤토리 페이지부터는 가로모드 허용)
    OrientationHelper.enterAllOrientationsMode();

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);

    // 초기 데이터 로드 (한 번만 실행)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _initializeRealtimeSync();
    });

    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  /// 완전한 메모리 정리 - 행사 전환 시 크래시 방지
  Future<void> _performCompleteMemoryCleanup() async {
    try {
      LoggerUtils.methodStart('_performCompleteMemoryCleanup', tag: _tag);

      // 이미지 캐시 정리 (await 불필요)
      imageCache.clear();
      imageCache.clearLiveImages();
      
      // 네트워크 이미지 캐시 정리 (cached_network_image 플러그인)
      final cacheManager = DefaultCacheManager();
      await cacheManager.emptyCache();

      // 정적 리소스 정리 (메모리 누수 방지)
      await cleanupAppResources();

      // GC 강제 실행 (개발 모드에서만)
      if (kDebugMode) {
        LoggerUtils.logInfo('메모리 정리 완료 - 강제 GC 요청', tag: _tag);
      }

      LoggerUtils.methodEnd('_performCompleteMemoryCleanup', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('메모리 정리 중 오류: $e', tag: _tag);
    }
  }

  /// 이벤트 변경 후 데이터 새로고침
  Future<void> _refreshDataAfterEventChange() async {
    if (!mounted) return;

    try {
      LoggerUtils.methodStart('_refreshDataAfterEventChange', tag: _tag);

      // 강력한 메모리 정리 - 행사 전환 시 크래시 방지
      await _performCompleteMemoryCleanup();

      // 실시간 동기화가 활성화되어 있으므로 에러 상태만 클리어
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어 (데이터는 실시간 동기화로 자동 갱신됨)
      productNotifier.clearError();
      prepaymentNotifier.clearError();

      LoggerUtils.logInfo('행사 전환 - 메모리 정리 및 상태 클리어 완료', tag: _tag);
      LoggerUtils.methodEnd('_refreshDataAfterEventChange', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 변경 후 상태 클리어 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 동기화 초기화
  Future<void> _initializeRealtimeSync() async {
    try {
      LoggerUtils.methodStart('_initializeRealtimeSync', tag: _tag);
      
      // Firebase 인증 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('사용자가 로그인되지 않음 - 실시간 동기화 건너뛰기', tag: _tag);
        return;
      }
      
      // 실시간 동기화 서비스 준비 (RealtimeSyncService v2.0.0)
      final syncService = ref.read(realtimeSyncServiceProvider);
      
      // SyncStateNotifier 초기화
      final syncNotifier = ref.read(syncStateProvider.notifier);
      await syncNotifier.initialize();
      
      // 현재 워크스페이스가 있으면 구독 시작
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        final eventId = currentWorkspace.id;
        await syncService.subscribeToEvent(eventId);
        LoggerUtils.logInfo('행사 $eventId에 대한 실시간 구독 시작됨 (v2.0.0)', tag: _tag);
      }
      
      LoggerUtils.logInfo('실시간 동기화 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 초기화 실패', tag: _tag, error: e);
      // 실패해도 앱 동작에는 영향을 주지 않음
    }
  }

  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);

    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _searchController.dispose();

    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;
    if (!mounted) return;

    LoggerUtils.methodStart('_onTabChanged', tag: _tag);

    setState(() {
      _currentTabIndex = _tabController.index;
      // 탭 변경 시 검색 모드 해제
      if (_isSearchMode) {
        _isSearchMode = false;
        _searchController.clear();
        if (mounted) {
          ref.read(prepaymentNotifierProvider.notifier).searchPrepayments('');
        }
      }
    });

    // 탭 변경 시 앱바 액션들은 이미 _currentTabIndex 변경으로 자동 업데이트됨

    LoggerUtils.methodEnd('_onTabChanged', tag: _tag);
  }

  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_loadInitialData', tag: _tag);

    // 2단계 가드: 현재 행사가 설정되지 않은 경우 데이터 로딩 시도하지 않음
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 행사가 설정되지 않아 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      LoggerUtils.logWarning('행사 정보를 불러올 수 없어 데이터 로딩을 건너뜁니다', tag: _tag);
      LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
      return;
    }

    // 초기 데이터 로딩 (실시간 동기화와 별개로 필요)
    LoggerUtils.logInfo('초기 데이터 로딩 시작', tag: _tag);

    try {
      // ProductNotifier에서 데이터 로드 (서버 fallback 포함)
      await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);

      // PrepaymentNotifier에서 데이터 로드
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();

      LoggerUtils.logInfo('초기 데이터 로딩 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초기 데이터 로딩 실패', tag: _tag, error: e);
      // 실패해도 앱은 계속 실행
    }

    LoggerUtils.methodEnd('_loadInitialData', tag: _tag);
  }

  /// 검색 텍스트 변경 처리
  void _onSearchTextChanged(String query) {
    if (!mounted) return;

    LoggerUtils.methodStart('_onSearchTextChanged', tag: _tag);

    ErrorUtils.wrapError(
      context,
      () async {
        if (mounted) {
          await ref
              .read(prepaymentNotifierProvider.notifier)
              .searchPrepayments(query);
        }
      },
      errorMessage: '검색 중 오류가 발생했습니다',
      type: ErrorType.database,
      tag: _tag,
    );

    LoggerUtils.methodEnd('_onSearchTextChanged', tag: _tag);
  }

  /// 검색 모드 토글
  void _toggleSearchMode() {
    if (!mounted) return;

    LoggerUtils.methodStart('_toggleSearchMode', tag: _tag);

    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchController.clear();
        if (mounted) {
          ref.read(prepaymentNotifierProvider.notifier).searchPrepayments('');
        }
      }
    });

    LoggerUtils.methodEnd('_toggleSearchMode', tag: _tag);
  }

  @override
  Widget build(BuildContext context) {
    // 현재 워크스페이스 변경 감지 및 데이터 새로고침
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous != null && next != null && previous.id != next.id) {
        LoggerUtils.logInfo('현재 이벤트 변경 감지: ${previous.name} -> ${next.name}', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      } else if (previous == null && next != null) {
        // 3단계 가드: 현재 행사가 null에서 설정된 경우 (온보딩 완료 후)
        LoggerUtils.logInfo('현재 이벤트 설정됨: ${next.name} - 데이터 새로고침', tag: _tag);
        _refreshDataAfterEventChange();
        // Consumer 위젯들이 자동으로 상태 변화를 감지하므로 수동 setState 불필요
      }
    });

    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: _buildToolbarTitle(),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '재고 현황'),
            Tab(text: '선입금 목록'),
          ],
        ),
        actions: _buildAppBarActions(),
      ),
      drawer: _buildDrawer(),
      // onDrawerChanged 제거 - 드로어 열 때마다 불필요한 프로필 이미지 체크 방지
      body: Stack(
        children: [
          SafeArea(
            child: TabBarView(
              controller: _tabController,
              children: const [InventoryTab(), PrepaymentTab()],
            ),
          ),
          // 워크스페이스 전환 로딩 오버레이
          if (workspaceState.isLoading) _buildWorkspaceLoadingOverlay(workspaceState),
        ],
      ),
    );
  }

  /// 툴바 타이틀
  Widget _buildToolbarTitle() {
    // 선입금 탭에서 검색 모드인 경우 - 검색창 표시
    if (_currentTabIndex == 1 && _isSearchMode) {
      return TextField(
        controller: _searchController,
        autofocus: true,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white),
        cursorColor: Colors.white,
        decoration: InputDecoration(
          filled: true,
          fillColor: Colors.black.withValues(alpha: 0.15),
          hintText: '선입금 검색...',
          hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white70),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        ),
        onChanged: _onSearchTextChanged,
      );
    }

    return Stack(
      children: [
        // toolbar_title_seller_filter (재고 탭용)
        Visibility(
          visible: _currentTabIndex == 0 && !_isSearchMode,
          child: Consumer(
            builder: (context, ref, child) {
              final selectedSeller = ref.watch(selectedSellerFilterProvider);
              return GestureDetector(
                onTap: _showSellerFilterDialog,
                child: Text(
                  selectedSeller.isNotEmpty ? selectedSeller : '전체 (판매자)',
                  style: TextStyle(fontFamily: 'Pretendard', 
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
        ),

        // toolbar_title_day_filter (선입금 탭용)
        Visibility(
          visible: _currentTabIndex == 1 && !_isSearchMode,
          child: Consumer(
            builder: (context, ref, child) {
              final selectedDay = ref.watch(prepaymentDayOfWeekFilterProvider);
              return GestureDetector(
                onTap: _showDayFilterDialog,
                child: Text(
                  selectedDay == 0
                      ? '전체 (요일)'
                      : selectedDay == 8
                          ? '없음'
                          : _getDayOfWeekName(selectedDay),
                  style: TextStyle(fontFamily: 'Pretendard', 
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 요일 이름 반환 헬퍼 메서드
  String _getDayOfWeekName(int dayOfWeek) {
    switch (dayOfWeek) {
      case 1:
        return '월요일';
      case 2:
        return '화요일';
      case 3:
        return '수요일';
      case 4:
        return '목요일';
      case 5:
        return '금요일';
      case 6:
        return '토요일';
      case 7:
        return '일요일';
      default:
        return '전체';
    }
  }

  /// AppBar 액션들 빌드
  List<Widget> _buildAppBarActions() {
    List<Widget> actions = [];

    // 동기화 상태 아이콘 (모든 탭에 공통으로 표시)
    actions.add(
      const Padding(
        padding: EdgeInsets.only(right: 8.0),
        child: const Text('동기화 상태'),
      ),
    );

    // 재고 탭 액션들
    if (_currentTabIndex == 0) {
      actions.addAll([
        // 판매 (일괄 판매를 일반 판매로 변경)
        IconButton(
          icon: const Icon(Icons.shopping_cart_outlined),
          tooltip: '판매',
          onPressed: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SaleScreen(),
              ),
            );
            if (result == true) {
              // 판매 완료 후 재고현황은 실시간 동기화로 자동 갱신됨
              LoggerUtils.logInfo('판매 완료 - 실시간 동기화로 자동 갱신됨', tag: _tag);
            }
          },
        ),

        // 정렬
        IconButton(
          icon: const Icon(Icons.sort),
          tooltip: '정렬',
          onPressed: _showSortDialog,
        ),
      ]);
    }
    // 선입금 탭 액션들
    else if (_currentTabIndex == 1) {
      // 검색 버튼
      actions.add(
        IconButton(
          icon: Icon(_isSearchMode ? Icons.close : Icons.search),
          onPressed: _toggleSearchMode,
          tooltip: _isSearchMode ? '검색 취소' : '검색',
        ),
      );

      // QR코드 버튼 추가 (돋보기와 정렬 사이)
      actions.add(
        IconButton(
          icon: const Icon(Icons.qr_code_scanner),
          onPressed: _openQrScanScreen,
          tooltip: 'QR코드 스캔',
        ),
      );

      // 정렬 버튼
      if (!_isSearchMode) {
        actions.add(
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showPrepaymentSortDialog,
            tooltip: '정렬',
          ),
        );
      }
    }

    return actions;
  }

  /// 드로어 빌드
  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Consumer(
            builder: (context, ref, child) {
              final nickname = ref.watch(nicknameProvider);
              return _DrawerProfileHeader(nicknameObj: nickname);
            },
          ),

          // 행사 선택 메뉴
          ListTile(
            leading: const Icon(Icons.event),
            title: const Text('행사 선택'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const EventListScreen(),
                ),
              );
            },
          ),

          const Divider(),

          // [그룹] 상품/선입금 관리 확장형 메뉴
          ExpansionTile(
            leading: const Icon(Icons.shopping_basket),
            title: const Text('상품/선입금 관리'),
            children: [
              ListTile(
                leading: Icon(Icons.add_box, size: 18),
                title: const Text('상품 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    AnimationUtils.expressivePageRoute(
                      page: const RegisterProductScreen(),
                    ),
                  ).then((result) {
                    if (result == true) {
                      // 상품 등록 후 실시간 동기화로 자동 갱신됨
                      LoggerUtils.logInfo('상품 등록 완료 - 실시간 동기화로 자동 갱신됨', tag: _tag);
                    }
                  });
                },
              ),
              ListTile(
                leading: Icon(Icons.payment, size: 18),
                title: const Text('선입금 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    AnimationUtils.expressivePageRoute(
                      page: const RegisterPrepaymentScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.download, size: 18),
                title: const Text('엑셀로 데이터 등록', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () async {
                  Navigator.pop(context);
                  final mode = ref.read(excelImportModeProvider);
                  final settings = ref.read(settingsNotifierProvider).value;
                  final collectDayOfWeek = settings?.collectDayOfWeekFromExcel ?? false;
                  final dayOfWeekColumnIndex = settings?.excelDayOfWeekColumnIndex ?? -1;
                  final result = await showDialog<ExcelImportDialogResult>(
                    context: context,
                    builder: (context) => ExcelImportModeDialog(
                      initialMode: mode,
                      initialCollectDayOfWeek: collectDayOfWeek,
                      initialDayOfWeekColumnIndex: dayOfWeekColumnIndex,
                    ),
                  );
                  if (result != null) {
                    ref.read(excelImportModeProvider.notifier).state = result.selectedMode;
                    ref.read(settingsNotifierProvider.notifier).setCollectDayOfWeekFromExcel(result.collectDayOfWeek);
                    ref.read(settingsNotifierProvider.notifier).setExcelDayOfWeekColumnIndex(result.dayOfWeekColumnIndex);
                    Navigator.of(context).push(
                      AnimationUtils.expressivePageRoute(
                        page: ExcelImportScreen(
                          initialMode: result.selectedMode,
                          initialCollectDayOfWeek: result.collectDayOfWeek,
                          initialDayOfWeekColumnIndex: result.dayOfWeekColumnIndex,
                        ),
                      ),
                    ).then((result) async {
                      if (result == true) {
                        ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
                      }
                    });
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.inventory_2, size: 18),
                title: const Text('선입금 상품 관리', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    AnimationUtils.expressivePageRoute(
                      page: const PrepaymentVirtualProductManagementScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.link, size: 18),
                title: const Text('선입금-상품 연동 관리', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    AnimationUtils.expressivePageRoute(
                      page: const PrepaymentProductLinkScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.category, size: 18),
                title: const Text('카테고리 관리', style: TextStyle(fontSize: 14)),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                dense: true,
                onTap: () {
                  Navigator.pop(context);
                  Navigator.of(context).push(
                    AnimationUtils.expressivePageRoute(
                      page: const CategoryManagementScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
          // [기존 메뉴] 판매자 관리, 서비스 증정, 판매 기록, 통계, 설정 등은 그대로 유지
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('판매자 관리'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const SellerManagementScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.volunteer_activism),
            title: const Text('서비스 처리'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const ServiceScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.local_offer),
            title: const Text('세트 할인'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const SetDiscountScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.receipt_long),
            title: const Text('판매 기록'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const SalesHistoryScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.bar_chart),
            title: const Text('통계'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const StatisticsScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('설정'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                AnimationUtils.expressivePageRoute(
                  page: const SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 판매자 필터 다이얼로그 표시
  void _showSellerFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AnimationUtils.dialogTransition(
        animation: ModalRoute.of(context)!.animation!,
        child: Consumer(
          builder: (context, ref, child) {
            // 실시간 판매자 목록 감시
            final sellerAsync = ref.watch(sellerNotifierProvider);
            final sellerNames = sellerAsync.isLoading
                ? <String>[]
                : sellerAsync.hasError
                    ? <String>[]
                    : sellerAsync.sellers.map((s) => s.name).toList()..sort();

            return AlertDialog(
              title: const Text(
                '판매자 선택',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    ListTile(
                      title: const Text('전체', style: TextStyle(fontSize: 16)),
                      onTap: () {
                        ref
                            .read(productNotifierProvider.notifier)
                            .setSellerFilter('');
                        Navigator.pop(context);
                      },
                    ),
                    ...sellerNames.map(
                      (seller) => ListTile(
                        title: Text(
                          seller,
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
                        ),
                        onTap: () {
                          ref
                              .read(productNotifierProvider.notifier)
                              .setSellerFilter(seller);
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 요일 필터 다이얼로그 표시
  void _showDayFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AnimationUtils.dialogTransition(
        animation: ModalRoute.of(context)!.animation!,
        child: Consumer(
          builder: (context, ref, child) {
            final availableDays = ref.watch(prepaymentAvailableDaysOfWeekProvider);
            return AlertDialog(
              title: const Text(
                '요일 선택',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    ListTile(
                      title: const Text('전체', style: TextStyle(fontSize: 16)),
                      onTap: () {
                        ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = 0;
                        ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(0);
                        Navigator.pop(context);
                      },
                    ),
                    if (availableDays.isEmpty)
                      const ListTile(
                        title: Text('등록된 요일 없음', style: TextStyle(fontSize: 16, color: Colors.grey)),
                      ),
                    for (int i = 1; i <= 7; i++)
                      if (availableDays.contains(i))
                        ListTile(
                          title: Text(
                            _getDayOfWeekName(i),
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
                          ),
                          onTap: () {
                            ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = i;
                            ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(i);
                            Navigator.pop(context);
                          },
                        ),
                    // '없음' 요일 처리
                    if (availableDays.contains(8))
                      ListTile(
                        title: const Text('없음', style: TextStyle(fontSize: 16)),
                        onTap: () {
                          ref.read(prepaymentDayOfWeekFilterProvider.notifier).state = 8;
                          ref.read(prepaymentNotifierProvider.notifier).filterByDayOfWeek(8);
                          Navigator.pop(context);
                        },
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 상품 정렬 다이얼로그 표시 (선입금목록과 완전히 동일한 스타일)
  void _showSortDialog() {
    final currentOption = ref.watch(currentSortOptionProvider);
    showDialog(
      context: context,
      builder: (context) => AnimationUtils.dialogTransition(
        animation: ModalRoute.of(context)!.animation!,
        child: AlertDialog(
          title: const Text(
            '정렬 방식',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SizedBox(
            width: 400, // 가로길이 400px로 고정
            child: ListView(
              shrinkWrap: true,
              children: [
                _buildSortTile(
                  context: context,
                  title: '최근 추가순',
                  isSelected: currentOption == ProductSortOption.recentlyAdded,
                  isAscending: false,
                  onTap: () {
                    ref.read(productNotifierProvider.notifier).setSortOption(ProductSortOption.recentlyAdded);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '이름순',
                  isSelected: currentOption == ProductSortOption.nameAsc || currentOption == ProductSortOption.nameDesc,
                  isAscending: currentOption == ProductSortOption.nameAsc,
                  onTap: () {
                    final newOption = currentOption == ProductSortOption.nameAsc
                        ? ProductSortOption.nameDesc
                        : ProductSortOption.nameAsc;
                    ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '가격순',
                  isSelected: currentOption == ProductSortOption.priceAsc || currentOption == ProductSortOption.priceDesc,
                  isAscending: currentOption == ProductSortOption.priceAsc,
                  onTap: () {
                    final newOption = currentOption == ProductSortOption.priceAsc
                        ? ProductSortOption.priceDesc
                        : ProductSortOption.priceAsc;
                    ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '재고순',
                  isSelected: currentOption == ProductSortOption.quantityAsc || currentOption == ProductSortOption.quantityDesc,
                  isAscending: currentOption == ProductSortOption.quantityAsc,
                  onTap: () {
                    final newOption = currentOption == ProductSortOption.quantityAsc
                        ? ProductSortOption.quantityDesc
                        : ProductSortOption.quantityAsc;
                    ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 선입금 정렬 및 필터 다이얼로그 표시
  void _showPrepaymentSortDialog() {
    final currentOrder = ref.watch(prepaymentSortOrderProvider);
    final currentState = ref.watch(prepaymentNotifierProvider);
    showDialog(
      context: context,
      builder: (context) => AnimationUtils.dialogTransition(
        animation: ModalRoute.of(context)!.animation!,
        child: AlertDialog(
          title: const Text(
            '정렬 및 필터',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: [
                // 필터 섹션
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    '필터',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                ...PrepaymentFilter.values.map((filter) => _buildFilterTile(
                  context: context,
                  filter: filter,
                  isSelected: currentState.filter == filter,
                  onTap: () {
                    ref.read(prepaymentNotifierProvider.notifier).setFilter(filter);
                    Navigator.pop(context);
                  },
                )),

                const Divider(height: 32),

                // 정렬 섹션
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    '정렬',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                _buildSortTile(
                  context: context,
                  title: '이름순',
                  isSelected: currentOrder.isNameSort,
                  isAscending: currentOrder.isNameSort ? currentOrder.isAscending : true,
                  onTap: () {
                    final newOrder = currentOrder.isNameSort
                        ? currentOrder.toggleNameSort()
                        : PrepaymentSortOrder.buyerNameAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '작성 날짜순',
                  isSelected: currentOrder == PrepaymentSortOrder.writtenDateAsc || currentOrder == PrepaymentSortOrder.writtenDateDesc,
                  isAscending: currentOrder == PrepaymentSortOrder.writtenDateAsc,
                  onTap: () {
                    final newOrder = currentOrder == PrepaymentSortOrder.writtenDateAsc
                        ? PrepaymentSortOrder.writtenDateDesc
                        : PrepaymentSortOrder.writtenDateAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
                _buildSortTile(
                  context: context,
                  title: '금액순',
                  isSelected: currentOrder == PrepaymentSortOrder.amountAsc || currentOrder == PrepaymentSortOrder.amountDesc,
                  isAscending: currentOrder == PrepaymentSortOrder.amountAsc,
                  onTap: () {
                    final newOrder = currentOrder == PrepaymentSortOrder.amountAsc
                        ? PrepaymentSortOrder.amountDesc
                        : PrepaymentSortOrder.amountAsc;
                    ref.read(prepaymentSortOrderProvider.notifier).state = newOrder;
                    ref.read(prepaymentNotifierProvider.notifier).loadPrepaymentsSorted(newOrder);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSortTile({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required bool isAscending,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.15) : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                isAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildFilterTile({
    required BuildContext context,
    required PrepaymentFilter filter,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.15) : Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: Text(
          filter.displayName,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                Icons.check,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  void _openQrScanScreen() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QrScanScreen(),
      ),
    );
    if (result is String && result.isNotEmpty) {
      // QR코드 결과가 URL이면 웹사이트로 이동
      final uri = Uri.tryParse(result);
      if (uri != null && (uri.isScheme('http') || uri.isScheme('https'))) {
        try {
          final canLaunch = await canLaunchUrl(uri);
          if (canLaunch) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          } else {
            // canLaunchUrl이 false를 반환해도 실제로는 열릴 수 있으므로 시도
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('URL을 열 수 없습니다: $result')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('QR코드 결과: $result')),
        );
      }
    }
  }

  /// 워크스페이스 전환 로딩 오버레이
  Widget _buildWorkspaceLoadingOverlay(UnifiedWorkspaceState workspaceState) {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  workspaceState.loadingMessage ?? '행사 전환 중...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (workspaceState.errorMessage != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    workspaceState.errorMessage!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// DrawerHeader 대체: 로그인한 사용자의 닉네임을 Firestore에서 직접 불러와 표시
class _UserNicknameHeader extends StatefulWidget {
  const _UserNicknameHeader();

  @override
  State<_UserNicknameHeader> createState() => _UserNicknameHeaderState();
}

class _UserNicknameHeaderState extends State<_UserNicknameHeader> {
  String? _nickname;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadNickname();
  }

  Future<void> _loadNickname() async {
    setState(() { _loading = true; });
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
          _nickname = doc.data()!['nickname'] as String;
        }
      }
    } catch (_) {}
    setState(() { _loading = false; });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(color: Colors.blue),
      padding: const EdgeInsets.only(top: 32, left: 16, right: 16, bottom: 16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32,
            backgroundColor: Colors.white,
            child: Icon(Icons.person, size: 40, color: Colors.blue),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _loading
                    ? const SizedBox(height: 24, width: 80, child: LinearProgressIndicator())
                    : Text(
                        _nickname ?? '닉네임 없음',
                        style: TextStyle(fontFamily: 'Pretendard', 
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).pushNamed('/my_page');
                  },
                  child: const Text(
                    '마이페이지',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _DrawerProfileHeader extends StatelessWidget {
  final dynamic nicknameObj; // Nickname? 타입으로 받음
  const _DrawerProfileHeader({this.nicknameObj});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.primary),
      padding: const EdgeInsets.only(top: 32, left: 16, right: 16, bottom: 16),
      child: Row(
        children: [
          ProfileAvatarWidget(
            radius: 32,
            nickname: nicknameObj,
            backgroundColor: Colors.white,
            iconColor: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nicknameObj?.name ?? '닉네임 없음',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).pushNamed('/my_page');
                  },
                  child: const Text(
                    '마이페이지',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}






