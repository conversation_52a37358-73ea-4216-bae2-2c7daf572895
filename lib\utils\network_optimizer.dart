import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import 'logger_utils.dart';

/// 네트워크 요청 타입
enum RequestType {
  get,
  post,
  put,
  delete,
  patch,
}

/// 캐시 정책
enum CachePolicy {
  noCache,
  memoryOnly,
  diskOnly,
  memoryAndDisk,
  networkOnly,
  networkFirst,
  cacheFirst,
}

/// 네트워크 상태
enum NetworkState {
  unknown,
  connected,
  disconnected,
  wifi,
  mobile,
}

/// 캐시된 응답
class CachedResponse {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime expiresAt;
  final Map<String, String> headers;
  final int statusCode;
  final String etag;

  CachedResponse({
    required this.key,
    required this.data,
    required this.createdAt,
    required this.expiresAt,
    required this.headers,
    required this.statusCode,
    required this.etag,
  });

  /// 캐시가 유효한지 확인
  bool get isValid => DateTime.now().isBefore(expiresAt);

  /// 캐시 수명 (초)
  int get ageInSeconds => DateTime.now().difference(createdAt).inSeconds;

  /// JSON 직렬화
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'headers': headers,
      'statusCode': statusCode,
      'etag': etag,
    };
  }

  /// JSON 역직렬화
  factory CachedResponse.fromJson(Map<String, dynamic> json) {
    return CachedResponse(
      key: json['key'],
      data: json['data'],
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: DateTime.parse(json['expiresAt']),
      headers: Map<String, String>.from(json['headers']),
      statusCode: json['statusCode'],
      etag: json['etag'],
    );
  }
}

/// 네트워크 요청 설정
class NetworkRequestConfig {
  final RequestType type;
  final String url;
  final Map<String, String>? headers;
  final dynamic body;
  final Duration timeout;
  final CachePolicy cachePolicy;
  final Duration cacheDuration;
  final bool retryOnFailure;
  final int maxRetries;
  final Duration retryDelay;

  const NetworkRequestConfig({
    required this.type,
    required this.url,
    this.headers,
    this.body,
    this.timeout = const Duration(seconds: 30),
    this.cachePolicy = CachePolicy.networkFirst,
    this.cacheDuration = const Duration(minutes: 5),
    this.retryOnFailure = true,
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
  });

  /// 기본 GET 요청
  factory NetworkRequestConfig.get(String url) {
    return NetworkRequestConfig(
      type: RequestType.get,
      url: url,
      cachePolicy: CachePolicy.networkFirst,
    );
  }

  /// POST 요청
  factory NetworkRequestConfig.post(String url, {dynamic body}) {
    return NetworkRequestConfig(
      type: RequestType.post,
      url: url,
      body: body,
      cachePolicy: CachePolicy.noCache,
    );
  }

  /// 복사 생성자
  NetworkRequestConfig copyWith({
    RequestType? type,
    String? url,
    Map<String, String>? headers,
    dynamic body,
    Duration? timeout,
    CachePolicy? cachePolicy,
    Duration? cacheDuration,
    bool? retryOnFailure,
    int? maxRetries,
    Duration? retryDelay,
  }) {
    return NetworkRequestConfig(
      type: type ?? this.type,
      url: url ?? this.url,
      headers: headers ?? this.headers,
      body: body ?? this.body,
      timeout: timeout ?? this.timeout,
      cachePolicy: cachePolicy ?? this.cachePolicy,
      cacheDuration: cacheDuration ?? this.cacheDuration,
      retryOnFailure: retryOnFailure ?? this.retryOnFailure,
      maxRetries: maxRetries ?? this.maxRetries,
      retryDelay: retryDelay ?? this.retryDelay,
    );
  }
}

/// 네트워크 응답
class NetworkResponse {
  final dynamic data;
  final int statusCode;
  final Map<String, String> headers;
  final bool fromCache;
  final Duration responseTime;
  final String? etag;

  NetworkResponse({
    required this.data,
    required this.statusCode,
    required this.headers,
    this.fromCache = false,
    required this.responseTime,
    this.etag,
  });

  /// 성공 여부
  bool get isSuccess => statusCode >= 200 && statusCode < 300;

  /// 캐시 가능 여부
  bool get isCacheable => statusCode == 200 && !fromCache;
}

/// 네트워크 최적화 설정
class NetworkOptimizerConfig {
  final int maxMemoryCacheSize;
  final int maxDiskCacheSize;
  final Duration defaultCacheDuration;
  final bool enableCompression;
  final bool enableRequestBatching;
  final Duration batchTimeout;
  final int maxBatchSize;
  final bool enableRequestDeduplication;
  final Duration deduplicationWindow;

  const NetworkOptimizerConfig({
    this.maxMemoryCacheSize = 100,
    this.maxDiskCacheSize = 1000,
    this.defaultCacheDuration = const Duration(minutes: 10),
    this.enableCompression = true,
    this.enableRequestBatching = true,
    this.batchTimeout = const Duration(milliseconds: 500),
    this.maxBatchSize = 10,
    this.enableRequestDeduplication = true,
    this.deduplicationWindow = const Duration(seconds: 5),
  });

  /// 기본 설정
  factory NetworkOptimizerConfig.defaultConfig() {
    return const NetworkOptimizerConfig();
  }

  /// 고성능 설정
  factory NetworkOptimizerConfig.highPerformance() {
    return const NetworkOptimizerConfig(
      maxMemoryCacheSize: 200,
      maxDiskCacheSize: 2000,
      defaultCacheDuration: const Duration(minutes: 5),
      enableCompression: true,
      enableRequestBatching: true,
      batchTimeout: const Duration(milliseconds: 200),
      maxBatchSize: 20,
      enableRequestDeduplication: true,
      deduplicationWindow: const Duration(seconds: 2),
    );
  }

  /// 데이터 절약 설정
  factory NetworkOptimizerConfig.dataSaving() {
    return const NetworkOptimizerConfig(
      maxMemoryCacheSize: 50,
      maxDiskCacheSize: 500,
      defaultCacheDuration: const Duration(hours: 1),
      enableCompression: true,
      enableRequestBatching: true,
      batchTimeout: const Duration(seconds: 2),
      maxBatchSize: 5,
      enableRequestDeduplication: true,
      deduplicationWindow: const Duration(seconds: 10),
    );
  }

  /// JSON 직렬화
  Map<String, dynamic> toJson() {
    return {
      'maxMemoryCacheSize': maxMemoryCacheSize,
      'maxDiskCacheSize': maxDiskCacheSize,
      'defaultCacheDuration': defaultCacheDuration.inMilliseconds,
      'enableCompression': enableCompression,
      'enableRequestBatching': enableRequestBatching,
      'batchTimeout': batchTimeout.inMilliseconds,
      'maxBatchSize': maxBatchSize,
      'enableRequestDeduplication': enableRequestDeduplication,
      'deduplicationWindow': deduplicationWindow.inMilliseconds,
    };
  }
}

/// 네트워크 최적화기
class NetworkOptimizer {
  final NetworkOptimizerConfig config;
  
  // 캐시
  final Map<String, CachedResponse> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // 배치 처리
  final Map<String, List<Completer<NetworkResponse>>> _batchRequests = {};
  final Map<String, Timer> _batchTimers = {};
  
  // 중복 요청 방지
  final Map<String, Completer<NetworkResponse>> _pendingRequests = {};
  
  // 네트워크 상태
  NetworkState _networkState = NetworkState.unknown;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // 통계
  int _totalRequests = 0;
  int _cachedResponses = 0;
  int _batchRequestCount = 0;
  int _deduplicatedRequests = 0;
  final List<Duration> _responseTimes = [];

  // 콜백 함수들
  void Function(NetworkState)? onNetworkStateChanged;
  void Function(String, CachedResponse)? onResponseCached;
  void Function(String)? onResponseEvicted;

  NetworkOptimizer({
    NetworkOptimizerConfig? config,
  }) : config = config ?? NetworkOptimizerConfig.defaultConfig();

  /// 초기화
  Future<void> initialize() async {
    LoggerUtils.logDebug(
      'Initializing network optimizer',
      tag: 'NetworkOptimizer',
    );

    await _setupConnectivityListener();
    _startCacheCleanup();

    LoggerUtils.logDebug(
      'Network optimizer initialized',
      tag: 'NetworkOptimizer',
    );
  }

  /// 네트워크 요청 실행
  Future<NetworkResponse> executeRequest(NetworkRequestConfig requestConfig) async {
    _totalRequests++;
    final startTime = DateTime.now();

    try {
      // 캐시 키 생성
      final cacheKey = _generateCacheKey(requestConfig);

      // 중복 요청 확인
      if (config.enableRequestDeduplication && _pendingRequests.containsKey(cacheKey)) {
        _deduplicatedRequests++;
        LoggerUtils.logDebug(
          'Deduplicating request: ${requestConfig.url}',
          tag: 'NetworkOptimizer',
        );
        return await _pendingRequests[cacheKey]!.future;
      }

      // 배치 처리 확인
      if (config.enableRequestBatching && _shouldBatch(requestConfig)) {
        return await _executeBatchedRequest(requestConfig);
      }

      // 캐시 정책에 따른 처리
      switch (requestConfig.cachePolicy) {
        case CachePolicy.noCache:
          return await _executeNetworkRequest(requestConfig, startTime);
        case CachePolicy.memoryOnly:
          return await _executeWithMemoryCache(requestConfig, startTime);
        case CachePolicy.diskOnly:
          return await _executeWithDiskCache(requestConfig, startTime);
        case CachePolicy.memoryAndDisk:
          return await _executeWithFullCache(requestConfig, startTime);
        case CachePolicy.networkOnly:
          return await _executeNetworkRequest(requestConfig, startTime);
        case CachePolicy.networkFirst:
          return await _executeNetworkFirst(requestConfig, startTime);
        case CachePolicy.cacheFirst:
          return await _executeCacheFirst(requestConfig, startTime);
      }
    } catch (e) {
      LoggerUtils.logError(
        'Request failed: ${requestConfig.url} - $e',
        error: e,
        tag: 'NetworkOptimizer',
      );
      rethrow;
    }
  }

  /// 네트워크 우선 요청
  Future<NetworkResponse> _executeNetworkFirst(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    try {
      // 네트워크 요청 시도
      final response = await _executeNetworkRequest(requestConfig, startTime);
      
      // 성공 시 캐시에 저장
      if (response.isCacheable) {
        _cacheResponse(requestConfig, response);
      }
      
      return response;
    } catch (e) {
      // 네트워크 실패 시 캐시에서 가져오기
      LoggerUtils.logWarning(
        'Network failed, trying cache: ${requestConfig.url}',
        tag: 'NetworkOptimizer',
      );
      
      final cachedResponse = _getCachedResponse(requestConfig);
      if (cachedResponse != null) {
        return NetworkResponse(
          data: cachedResponse.data,
          statusCode: cachedResponse.statusCode,
          headers: cachedResponse.headers,
          fromCache: true,
          responseTime: DateTime.now().difference(startTime),
          etag: cachedResponse.etag,
        );
      }
      
      rethrow;
    }
  }

  /// 캐시 우선 요청
  Future<NetworkResponse> _executeCacheFirst(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    // 캐시에서 먼저 확인
    final cachedResponse = _getCachedResponse(requestConfig);
    if (cachedResponse != null) {
      _cachedResponses++;
      return NetworkResponse(
        data: cachedResponse.data,
        statusCode: cachedResponse.statusCode,
        headers: cachedResponse.headers,
        fromCache: true,
        responseTime: DateTime.now().difference(startTime),
        etag: cachedResponse.etag,
      );
    }

    // 캐시에 없으면 네트워크 요청
    final response = await _executeNetworkRequest(requestConfig, startTime);
    
    if (response.isCacheable) {
      _cacheResponse(requestConfig, response);
    }
    
    return response;
  }

  /// 메모리 캐시와 함께 요청
  Future<NetworkResponse> _executeWithMemoryCache(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    final cachedResponse = _getCachedResponse(requestConfig);
    if (cachedResponse != null) {
      _cachedResponses++;
      return NetworkResponse(
        data: cachedResponse.data,
        statusCode: cachedResponse.statusCode,
        headers: cachedResponse.headers,
        fromCache: true,
        responseTime: DateTime.now().difference(startTime),
        etag: cachedResponse.etag,
      );
    }

    final response = await _executeNetworkRequest(requestConfig, startTime);
    
    if (response.isCacheable) {
      _cacheResponse(requestConfig, response);
    }
    
    return response;
  }

  /// 디스크 캐시와 함께 요청
  Future<NetworkResponse> _executeWithDiskCache(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    // 디스크 캐시 구현 (실제로는 파일 시스템 사용)
    final response = await _executeNetworkRequest(requestConfig, startTime);
    
    if (response.isCacheable) {
      _cacheResponse(requestConfig, response);
    }
    
    return response;
  }

  /// 전체 캐시와 함께 요청
  Future<NetworkResponse> _executeWithFullCache(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    // 메모리 캐시 먼저 확인
    final cachedResponse = _getCachedResponse(requestConfig);
    if (cachedResponse != null) {
      _cachedResponses++;
      return NetworkResponse(
        data: cachedResponse.data,
        statusCode: cachedResponse.statusCode,
        headers: cachedResponse.headers,
        fromCache: true,
        responseTime: DateTime.now().difference(startTime),
        etag: cachedResponse.etag,
      );
    }

    final response = await _executeNetworkRequest(requestConfig, startTime);
    
    if (response.isCacheable) {
      _cacheResponse(requestConfig, response);
    }
    
    return response;
  }

  /// 네트워크 요청 실행
  Future<NetworkResponse> _executeNetworkRequest(
    NetworkRequestConfig requestConfig,
    DateTime startTime,
  ) async {
    final cacheKey = _generateCacheKey(requestConfig);
    final completer = Completer<NetworkResponse>();
    
    // 중복 요청 방지
    if (config.enableRequestDeduplication) {
      _pendingRequests[cacheKey] = completer;
    }

    try {
      final response = await _performHttpRequest(requestConfig);
      final responseTime = DateTime.now().difference(startTime);
      
      _responseTimes.add(responseTime);
      if (_responseTimes.length > 100) {
        _responseTimes.removeAt(0);
      }

      completer.complete(response);
      return response;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      if (config.enableRequestDeduplication) {
        _pendingRequests.remove(cacheKey);
      }
    }
  }

  /// HTTP 요청 수행
  Future<NetworkResponse> _performHttpRequest(NetworkRequestConfig requestConfig) async {
    final client = HttpClient();
    
    try {
      final request = await client.openUrl(
        _getHttpMethod(requestConfig.type),
        Uri.parse(requestConfig.url),
      );

      // 헤더 설정
      if (requestConfig.headers != null) {
        requestConfig.headers!.forEach((key, value) {
          request.headers.set(key, value);
        });
      }

      // 압축 헤더 추가
      if (config.enableCompression) {
        request.headers.set('Accept-Encoding', 'gzip, deflate');
      }

      // ETag 헤더 추가 (캐시된 응답이 있는 경우)
      final cachedResponse = _getCachedResponse(requestConfig);
      if (cachedResponse?.etag != null) {
        request.headers.set('If-None-Match', cachedResponse!.etag);
      }

      // 요청 본문 설정
      if (requestConfig.body != null) {
        final bodyBytes = utf8.encode(jsonEncode(requestConfig.body));
        request.contentLength = bodyBytes.length;
        request.add(bodyBytes);
      }

      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      final data = jsonDecode(responseBody);
      final etag = response.headers.value('etag');

      return NetworkResponse(
        data: data,
        statusCode: response.statusCode,
        headers: {}, // HttpHeaders를 Map으로 변환하는 것은 복잡하므로 빈 맵 사용
        responseTime: Duration.zero, // 실제 응답 시간은 호출자에서 계산
        etag: etag,
      );
    } finally {
      client.close();
    }
  }

  /// 배치 요청 실행
  Future<NetworkResponse> _executeBatchedRequest(NetworkRequestConfig requestConfig) async {
    final batchKey = _getBatchKey(requestConfig);
    final completer = Completer<NetworkResponse>();
    
    if (!_batchRequests.containsKey(batchKey)) {
      _batchRequests[batchKey] = [];
      
      // 배치 타이머 시작
      _batchTimers[batchKey] = Timer(config.batchTimeout, () {
        _executeBatch(batchKey);
      });
    }
    
    _batchRequests[batchKey]!.add(completer);
    
    // 배치 크기 도달 시 즉시 실행
    if (_batchRequests[batchKey]!.length >= config.maxBatchSize) {
      _batchTimers[batchKey]?.cancel();
      _executeBatch(batchKey);
    }
    
    return await completer.future;
  }

  /// 배치 실행
  void _executeBatch(String batchKey) {
    final requests = _batchRequests[batchKey];
    if (requests == null || requests.isEmpty) return;

    _batchRequests.remove(batchKey);
    _batchTimers.remove(batchKey)?.cancel();
    
            _batchRequestCount++;
    
    // 배치 요청 실행 (실제로는 여러 요청을 하나로 묶어서 처리)
    for (final completer in requests) {
      // 개별 요청으로 처리 (실제 배치 구현은 더 복잡)
      completer.completeError('Batch not implemented');
    }
  }

  /// 배치 키 생성
  String _getBatchKey(NetworkRequestConfig requestConfig) {
    return '${requestConfig.type.name}_${requestConfig.url}';
  }

  /// 배치 여부 확인
  bool _shouldBatch(NetworkRequestConfig requestConfig) {
    return requestConfig.type == RequestType.get && 
           _networkState == NetworkState.connected;
  }

  /// 캐시 키 생성
  String _generateCacheKey(NetworkRequestConfig requestConfig) {
    final keyData = {
      'type': requestConfig.type.name,
      'url': requestConfig.url,
      'headers': requestConfig.headers,
      'body': requestConfig.body,
    };
    
    final keyString = jsonEncode(keyData);
    final bytes = utf8.encode(keyString);
    final digest = sha256.convert(bytes);
    
    return digest.toString();
  }

  /// 캐시된 응답 가져오기
  CachedResponse? _getCachedResponse(NetworkRequestConfig requestConfig) {
    final cacheKey = _generateCacheKey(requestConfig);
    final cachedResponse = _memoryCache[cacheKey];
    
    if (cachedResponse != null && cachedResponse.isValid) {
      return cachedResponse;
    }
    
    if (cachedResponse != null && !cachedResponse.isValid) {
      _memoryCache.remove(cacheKey);
      _cacheTimestamps.remove(cacheKey);
    }
    
    return null;
  }

  /// 응답 캐시에 저장
  void _cacheResponse(NetworkRequestConfig requestConfig, NetworkResponse response) {
    final cacheKey = _generateCacheKey(requestConfig);
    final expiresAt = DateTime.now().add(requestConfig.cacheDuration);
    
    final cachedResponse = CachedResponse(
      key: cacheKey,
      data: response.data,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      headers: response.headers,
      statusCode: response.statusCode,
      etag: response.etag ?? '',
    );

    _memoryCache[cacheKey] = cachedResponse;
    _cacheTimestamps[cacheKey] = DateTime.now();
    
    // 캐시 크기 제한
    if (_memoryCache.length > config.maxMemoryCacheSize) {
      _evictOldestCache();
    }

    onResponseCached?.call(cacheKey, cachedResponse);
  }

  /// 가장 오래된 캐시 제거
  void _evictOldestCache() {
    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _cacheTimestamps.entries) {
      if (oldestTime == null || entry.value.isBefore(oldestTime)) {
        oldestTime = entry.value;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _memoryCache.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
      onResponseEvicted?.call(oldestKey);
    }
  }

  /// 캐시 정리 시작
  void _startCacheCleanup() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupCache();
    });
  }

  /// 캐시 정리
  void _cleanupCache() {
    final expiredKeys = <String>[];

    for (final entry in _memoryCache.entries) {
      if (!entry.value.isValid) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      onResponseEvicted?.call(key);
    }

    if (expiredKeys.isNotEmpty) {
      LoggerUtils.logDebug(
        'Cleaned up ${expiredKeys.length} expired cache entries',
        tag: 'NetworkOptimizer',
      );
    }
  }

  /// HTTP 메서드 변환
  String _getHttpMethod(RequestType type) {
    switch (type) {
      case RequestType.get:
        return 'GET';
      case RequestType.post:
        return 'POST';
      case RequestType.put:
        return 'PUT';
      case RequestType.delete:
        return 'DELETE';
      case RequestType.patch:
        return 'PATCH';
    }
  }

  /// 네트워크 연결 상태 리스너 설정
  Future<void> _setupConnectivityListener() async {
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
          final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
          _onConnectivityChanged(result);
        });
  }

  /// 네트워크 연결 상태 변경 처리
  void _onConnectivityChanged(ConnectivityResult result) {
    final previousState = _networkState;
    
    switch (result) {
      case ConnectivityResult.wifi:
        _networkState = NetworkState.wifi;
        break;
      case ConnectivityResult.mobile:
        _networkState = NetworkState.mobile;
        break;
      case ConnectivityResult.none:
        _networkState = NetworkState.disconnected;
        break;
      default:
        _networkState = NetworkState.connected;
    }

    if (_networkState != previousState) {
      onNetworkStateChanged?.call(_networkState);
      
      LoggerUtils.logInfo(
        'Network state changed: ${previousState.name} -> ${_networkState.name}',
        tag: 'NetworkOptimizer',
      );
    }
  }

  /// 캐시 무효화
  void invalidateCache([String? pattern]) {
    if (pattern == null) {
      // 전체 캐시 무효화
      final count = _memoryCache.length;
      _memoryCache.clear();
      _cacheTimestamps.clear();
      
      LoggerUtils.logInfo(
        'Invalidated entire cache ($count items)',
        tag: 'NetworkOptimizer',
      );
    } else {
      // 패턴에 맞는 캐시만 무효화
      final keysToRemove = _memoryCache.keys
          .where((key) => key.contains(pattern))
          .toList();
      
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
      
      LoggerUtils.logInfo(
        'Invalidated ${keysToRemove.length} cache items matching pattern: $pattern',
        tag: 'NetworkOptimizer',
      );
    }
  }

  /// 통계 정보
  Map<String, dynamic> getStats() {
    final avgResponseTime = _responseTimes.isNotEmpty
        ? _responseTimes
            .map((d) => d.inMilliseconds)
            .reduce((a, b) => a + b) / _responseTimes.length
        : 0.0;

    return {
      'totalRequests': _totalRequests,
      'cachedResponses': _cachedResponses,
      'batchRequests': _batchRequestCount,
      'deduplicatedRequests': _deduplicatedRequests,
      'cacheSize': _memoryCache.length,
      'maxCacheSize': config.maxMemoryCacheSize,
      'networkState': _networkState.name,
      'averageResponseTime': avgResponseTime,
      'cacheHitRate': _totalRequests > 0 ? _cachedResponses / _totalRequests : 0.0,
    };
  }

  /// 리소스 정리
  void dispose() {
    _connectivitySubscription?.cancel();
    
    // 배치 타이머 정리
    for (final timer in _batchTimers.values) {
      timer.cancel();
    }
    _batchTimers.clear();
    
    // 대기 중인 요청 정리
    for (final completer in _pendingRequests.values) {
      completer.completeError('Network optimizer disposed');
    }
    _pendingRequests.clear();
    
    // 캐시 정리
    _memoryCache.clear();
    _cacheTimestamps.clear();
    
    LoggerUtils.logDebug(
      'Network optimizer disposed',
      tag: 'NetworkOptimizer',
    );
  }

  /// 정적 리소스 정리 (앱 종료 시 호출)
  static void shutdown() {
    // NetworkOptimizer는 인스턴스 기반이므로 개별적으로 dispose 호출 필요
    LoggerUtils.logInfo('NetworkOptimizer 정리 완료');
  }
} 