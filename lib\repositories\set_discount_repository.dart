import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../models/set_discount.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 세트 할인 데이터 접근을 담당하는 Repository 클래스입니다.
/// - CRUD 작업, 필터링, 정렬 등의 데이터베이스 작업 수행
/// - 행사별 데이터 분리 관리
class SetDiscountRepository {
  final DatabaseService database;

  SetDiscountRepository({required this.database});

  /// 모든 세트 할인 조회 (특정 행사)
  Future<List<SetDiscount>> getAllSetDiscounts({int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;
      
      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ?',
          whereArgs: [eventId],
          orderBy: 'createdAt DESC',
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          orderBy: 'createdAt DESC',
        );
      }

      return maps.map((map) => SetDiscount.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get all set discounts', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// 활성화된 세트 할인만 조회 (특정 행사)
  Future<List<SetDiscount>> getActiveSetDiscounts({int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;
      
      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ? AND isActive = 1',
          whereArgs: [eventId],
          orderBy: 'createdAt DESC',
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'isActive = 1',
          orderBy: 'createdAt DESC',
        );
      }

      return maps.map((map) => SetDiscount.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get active set discounts', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// ID로 세트 할인 조회
  Future<SetDiscount?> getSetDiscountById(int id) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.setDiscountsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return SetDiscount.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get set discount by id: $id', error: e, tag: 'SetDiscountRepository');
      return null;
    }
  }

  /// 세트 할인 추가
  Future<int?> insertSetDiscount(SetDiscount setDiscount) async {
    try {
      final db = await database.database;
      final id = await db.insert(
        DatabaseServiceImpl.setDiscountsTable,
        setDiscount.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      LoggerUtils.logInfo('Set discount inserted with id: $id', tag: 'SetDiscountRepository');
      return id;
    } catch (e) {
      LoggerUtils.logError('Failed to insert set discount', error: e, tag: 'SetDiscountRepository');
      return null;
    }
  }

  /// 세트 할인 수정
  Future<bool> updateSetDiscount(SetDiscount setDiscount) async {
    try {
      final db = await database.database;
      final updatedSetDiscount = setDiscount.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final count = await db.update(
        DatabaseServiceImpl.setDiscountsTable,
        updatedSetDiscount.toMap(),
        where: 'id = ?',
        whereArgs: [setDiscount.id],
      );
      
      LoggerUtils.logInfo('Set discount updated: ${setDiscount.id}', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to update set discount: ${setDiscount.id}', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 세트 할인 삭제
  Future<bool> deleteSetDiscount(int id) async {
    try {
      final db = await database.database;
      final count = await db.delete(
        DatabaseServiceImpl.setDiscountsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggerUtils.logInfo('Set discount deleted: $id', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to delete set discount: $id', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 세트 할인 활성화/비활성화
  Future<bool> toggleSetDiscountActive(int id, bool isActive) async {
    try {
      final db = await database.database;
      final count = await db.update(
        DatabaseServiceImpl.setDiscountsTable,
        {
          'isActive': isActive ? 1 : 0,
          'updatedAt': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggerUtils.logInfo('Set discount active status changed: $id -> $isActive', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to toggle set discount active: $id', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 특정 상품이 포함된 세트 할인 조회
  Future<List<SetDiscount>> getSetDiscountsByProductId(int productId, {int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;
      
      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ? AND isActive = 1',
          whereArgs: [eventId],
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'isActive = 1',
        );
      }

      // productIds JSON에서 해당 상품 ID가 포함된 세트 할인만 필터링
      final setDiscounts = maps.map((map) => SetDiscount.fromMap(map)).toList();
      return setDiscounts.where((setDiscount) => 
        setDiscount.productIds.contains(productId)
      ).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get set discounts by product id: $productId', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// 세트 할인 이름 중복 확인
  Future<bool> isNameExists(String name, {int? eventId, int? excludeId}) async {
    try {
      final db = await database.database;
      String whereClause = 'name = ?';
      List<dynamic> whereArgs = [name];
      
      if (eventId != null) {
        whereClause += ' AND eventId = ?';
        whereArgs.add(eventId);
      }
      
      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.setDiscountsTable,
        where: whereClause,
        whereArgs: whereArgs,
      );

      return maps.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('Failed to check name exists: $name', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }
}
