import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/set_discount.dart';
import '../../providers/set_discount_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/current_event_provider.dart';
import '../../utils/currency_utils.dart';
import '../../utils/logger_utils.dart';

/// 세트 할인 등록/수정 다이얼로그
class SetDiscountFormDialog extends ConsumerStatefulWidget {
  final SetDiscount? setDiscount; // null이면 새 등록, 값이 있으면 수정

  const SetDiscountFormDialog({
    super.key,
    this.setDiscount,
  });

  @override
  ConsumerState<SetDiscountFormDialog> createState() => _SetDiscountFormDialogState();
}

class _SetDiscountFormDialogState extends ConsumerState<SetDiscountFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _discountAmountController = TextEditingController();
  
  final Set<int> _selectedProductIds = <int>{};
  bool _isLoading = false;
  String? _errorMessage;

  bool get _isEditing => widget.setDiscount != null;

  @override
  void initState() {
    super.initState();
    
    // 수정 모드인 경우 기존 값으로 초기화
    if (_isEditing) {
      final setDiscount = widget.setDiscount!;
      _nameController.text = setDiscount.name;
      _discountAmountController.text = setDiscount.discountAmount.toString();
      _selectedProductIds.addAll(setDiscount.productIds);
    }

    // 상품 목록 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _discountAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(productNotifierProvider);
    final products = productState.products;

    return AlertDialog(
      title: Text(_isEditing ? '세트 할인 수정' : '세트 할인 등록'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 세트 이름 입력
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: '세트 이름',
                  hintText: '예: 커플 세트, 패밀리 세트',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '세트 이름을 입력해주세요';
                  }
                  if (value.trim().length > 50) {
                    return '세트 이름은 50자 이내로 입력해주세요';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 할인 금액 입력
              TextFormField(
                controller: _discountAmountController,
                decoration: const InputDecoration(
                  labelText: '할인 금액',
                  hintText: '원',
                  border: OutlineInputBorder(),
                  suffixText: '원',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '할인 금액을 입력해주세요';
                  }
                  final amount = int.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return '올바른 할인 금액을 입력해주세요';
                  }
                  if (amount > 1000000) {
                    return '할인 금액은 100만원 이하로 입력해주세요';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 상품 선택 섹션
              const Text(
                '포함할 상품 선택',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // 에러 메시지 표시
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),

              // 상품 목록
              Flexible(
                child: Container(
                  height: 200,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: products.isEmpty
                      ? const Center(
                          child: Text(
                            '등록된 상품이 없습니다',
                            style: TextStyle(color: Colors.grey),
                          ),
                        )
                      : ListView.builder(
                          itemCount: products.length,
                          itemBuilder: (context, index) {
                            final product = products[index];
                            final isSelected = _selectedProductIds.contains(product.id);
                            
                            return CheckboxListTile(
                              title: Text(product.name),
                              subtitle: Text(
                                '${CurrencyUtils.formatCurrency(product.price)} • 재고: ${product.quantity}개',
                              ),
                              value: isSelected,
                              onChanged: (bool? value) {
                                setState(() {
                                  if (value == true) {
                                    _selectedProductIds.add(product.id!);
                                  } else {
                                    _selectedProductIds.remove(product.id!);
                                  }
                                  _errorMessage = null; // 에러 메시지 클리어
                                });
                              },
                              dense: true,
                            );
                          },
                        ),
                ),
              ),

              const SizedBox(height: 8),
              Text(
                '선택된 상품: ${_selectedProductIds.length}개',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSubmit,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_isEditing ? '수정' : '등록'),
        ),
      ],
    );
  }

  /// 폼 제출 처리
  void _handleSubmit() async {
    // 폼 유효성 검사
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 상품 선택 검사
    if (_selectedProductIds.isEmpty) {
      setState(() {
        _errorMessage = '최소 1개 이상의 상품을 선택해주세요';
      });
      return;
    }

    if (_selectedProductIds.length < 2) {
      setState(() {
        _errorMessage = '세트 할인은 최소 2개 이상의 상품이 필요합니다';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final name = _nameController.text.trim();
      final discountAmount = int.parse(_discountAmountController.text);
      final currentEventId = ref.read(currentEventIdProvider) ?? 1;

      // 이름 중복 검사
      final nameExists = await ref
          .read(setDiscountNotifierProvider.notifier)
          .isNameExists(name, excludeId: widget.setDiscount?.id);

      if (nameExists) {
        setState(() {
          _errorMessage = '이미 존재하는 세트 이름입니다';
          _isLoading = false;
        });
        return;
      }

      final setDiscount = SetDiscount.create(
        id: widget.setDiscount?.id,
        name: name,
        discountAmount: discountAmount,
        productIds: _selectedProductIds.toList(),
        eventId: currentEventId,
        createdAt: widget.setDiscount?.createdAt,
      );

      bool success;
      if (_isEditing) {
        success = await ref
            .read(setDiscountNotifierProvider.notifier)
            .updateSetDiscount(setDiscount);
      } else {
        success = await ref
            .read(setDiscountNotifierProvider.notifier)
            .addSetDiscount(setDiscount);
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing 
                  ? '세트 할인이 수정되었습니다'
                  : '세트 할인이 등록되었습니다',
            ),
          ),
        );
      } else {
        setState(() {
          _errorMessage = _isEditing 
              ? '세트 할인 수정에 실패했습니다'
              : '세트 할인 등록에 실패했습니다';
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerUtils.logError('Failed to save set discount', error: e, tag: 'SetDiscountFormDialog');
      setState(() {
        _errorMessage = '오류가 발생했습니다: ${e.toString()}';
        _isLoading = false;
      });
    }
  }
}
