import 'event.dart';

/// 행사 워크스페이스 모델
///
/// 각 행사 워크스페이스는 하나의 행사를 나타내며, 완전히 독립적인 데이터 공간을 가집니다.
/// 실제로는 Event 테이블의 데이터를 행사 워크스페이스 개념으로 추상화한 것입니다.
/// 데이터베이스에서는 eventId로 저장되지만, 사용자에게는 행사 워크스페이스로 표현됩니다.
class EventWorkspace {
  final int id;
  final String name;
  final String? imagePath;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final String? description;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const EventWorkspace({
    required this.id,
    required this.name,
    this.imagePath,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    this.description,
    required this.createdAt,
    this.updatedAt,
  });

  /// Event 객체로부터 EventWorkspace 생성
  factory EventWorkspace.fromEvent(Event event) {
    return EventWorkspace(
      id: event.id!,
      name: event.name,
      imagePath: event.imagePath,
      startDate: event.startDate,
      endDate: event.endDate,
      isActive: event.isActive,
      description: event.description,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
    );
  }

  /// Workspace를 Event 객체로 변환
  Event toEvent() {
    return Event(
      id: id,
      name: name,
      imagePath: imagePath,
      startDate: startDate,
      endDate: endDate,
      isActive: isActive,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// 행사 워크스페이스 복사 (일부 필드 변경)
  EventWorkspace copyWith({
    int? id,
    String? name,
    String? imagePath,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EventWorkspace(
      id: id ?? this.id,
      name: name ?? this.name,
      imagePath: imagePath ?? this.imagePath,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 워크스페이스 상태 확인
  WorkspaceStatus get status {
    final now = DateTime.now();
    if (now.isBefore(startDate)) {
      return WorkspaceStatus.upcoming;
    } else if (now.isAfter(endDate)) {
      return WorkspaceStatus.completed;
    } else {
      return WorkspaceStatus.ongoing;
    }
  }

  /// 행사 워크스페이스 기간 (일 수)
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }

  /// 행사 워크스페이스 진행률 (0.0 ~ 1.0)
  double get progress {
    final now = DateTime.now();
    if (now.isBefore(startDate)) return 0.0;
    if (now.isAfter(endDate)) return 1.0;
    
    final totalDuration = endDate.difference(startDate).inMilliseconds;
    final elapsed = now.difference(startDate).inMilliseconds;
    
    return (elapsed / totalDuration).clamp(0.0, 1.0);
  }

  /// 행사 워크스페이스 표시 이름 (상태 포함)
  String get displayName {
    switch (status) {
      case WorkspaceStatus.upcoming:
        return '$name (예정)';
      case WorkspaceStatus.ongoing:
        return '$name (진행중)';
      case WorkspaceStatus.completed:
        return '$name (완료)';
    }
  }

  /// 워크스페이스 날짜 설명
  String get dateDescription {
    final formatter = DateTime.now().year == startDate.year 
        ? 'M월 d일' 
        : 'yyyy년 M월 d일';
    
    final startStr = _formatDate(startDate, formatter);
    final endStr = _formatDate(endDate, formatter);
    
    if (startDate.year == endDate.year && 
        startDate.month == endDate.month && 
        startDate.day == endDate.day) {
      return startStr;
    } else {
      return '$startStr ~ $endStr';
    }
  }

  String _formatDate(DateTime date, String format) {
    // 간단한 날짜 포맷팅 (실제로는 intl 패키지 사용 권장)
    if (format == 'M월 d일') {
      return '${date.month}월 ${date.day}일';
    } else {
      return '${date.year}년 ${date.month}월 ${date.day}일';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventWorkspace && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EventWorkspace(id: $id, name: $name, status: $status)';
  }
}

/// 행사 워크스페이스 상태
enum WorkspaceStatus {
  upcoming,   // 예정
  ongoing,    // 진행중
  completed,  // 완료
}

/// 행사 워크스페이스 상태 확장
extension WorkspaceStatusExtension on WorkspaceStatus {
  String get displayName {
    switch (this) {
      case WorkspaceStatus.upcoming:
        return '예정';
      case WorkspaceStatus.ongoing:
        return '진행중';
      case WorkspaceStatus.completed:
        return '완료';
    }
  }

  /// 상태별 색상 (Material Design 색상 코드)
  int get colorValue {
    switch (this) {
      case WorkspaceStatus.upcoming:
        return 0xFF2196F3; // Blue
      case WorkspaceStatus.ongoing:
        return 0xFF4CAF50; // Green
      case WorkspaceStatus.completed:
        return 0xFF9E9E9E; // Grey
    }
  }
}
