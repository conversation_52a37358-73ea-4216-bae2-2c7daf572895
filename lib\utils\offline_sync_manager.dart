import 'dart:async';
import 'dart:convert';
import 'dart:collection';
import 'package:sqflite/sqflite.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'logger_utils.dart';
import 'offline_task.dart';

/// 동기화 상태
enum SyncStatus {
  idle,
  syncing,
  paused,
  error,
  completed,
}

/// 동기화 진행률
class SyncProgress {
  final int totalTasks;
  final int completedTasks;
  final int failedTasks;
  final int pendingTasks;
  final SyncStatus status;
  final String? currentTask;
  final DateTime? startTime;
  final Duration? estimatedTimeRemaining;

  SyncProgress({
    required this.totalTasks,
    required this.completedTasks,
    required this.failedTasks,
    required this.pendingTasks,
    required this.status,
    this.currentTask,
    this.startTime,
    this.estimatedTimeRemaining,
  });

  /// 진행률 (0.0 ~ 1.0)
  double get progress => totalTasks > 0 ? completedTasks / totalTasks : 0.0;

  /// 성공률
  double get successRate => totalTasks > 0 ? (completedTasks - failedTasks) / totalTasks : 0.0;

  /// 복사 생성자
  SyncProgress copyWith({
    int? totalTasks,
    int? completedTasks,
    int? failedTasks,
    int? pendingTasks,
    SyncStatus? status,
    String? currentTask,
    DateTime? startTime,
    Duration? estimatedTimeRemaining,
  }) {
    return SyncProgress(
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      failedTasks: failedTasks ?? this.failedTasks,
      pendingTasks: pendingTasks ?? this.pendingTasks,
      status: status ?? this.status,
      currentTask: currentTask ?? this.currentTask,
      startTime: startTime ?? this.startTime,
      estimatedTimeRemaining: estimatedTimeRemaining ?? this.estimatedTimeRemaining,
    );
  }
}

/// 오프라인 동기화 관리자
class OfflineSyncManager {
  final Database _database;
  final Map<String, OfflineTask> _pendingTasks = {};
  final Map<String, OfflineTask> _completedTasks = {};
  final Map<String, OfflineTask> _failedTasks = {};
  
  SyncStatus _status = SyncStatus.idle;
  SyncProgress _progress = SyncProgress(
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    pendingTasks: 0,
    status: SyncStatus.idle,
  );
  
  Timer? _syncTimer;
  Timer? _cleanupTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  final Duration _syncInterval = const Duration(minutes: 5);
  final Duration _cleanupInterval = const Duration(hours: 1);
  final int _maxConcurrentTasks = 3;

  // 콜백 함수들
  void Function(SyncProgress)? onProgressChanged;
  void Function(OfflineTask)? onTaskCompleted;
  void Function(OfflineTask, String)? onTaskFailed;
  void Function(SyncStatus)? onStatusChanged;

  OfflineSyncManager(this._database) {
    _initialize();
  }

  /// 초기화
  Future<void> _initialize() async {
    await _loadTasksFromDatabase();
    await _setupConnectivityListener();
    _startCleanupTimer();
    
    LoggerUtils.logDebug(
      'OfflineSyncManager initialized',
      tag: 'OfflineSyncManager',
    );
  }

  /// 데이터베이스에서 작업 로드
  Future<void> _loadTasksFromDatabase() async {
    try {
      final results = await _database.query(
        'offline_tasks',
        where: 'status = ?',
        whereArgs: ['pending'],
      );

      for (final row in results) {
        final task = OfflineTask.fromJson(jsonDecode(row['data'] as String));
        _pendingTasks[task.id] = task;
      }

      LoggerUtils.logDebug(
        'Loaded ${_pendingTasks.length} pending tasks from database',
        tag: 'OfflineSyncManager',
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to load tasks from database: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 작업 추가
  Future<String> addTask(OfflineTask task) async {
    _pendingTasks[task.id] = task;
    await _saveTaskToDatabase(task);
    
    _updateProgress();
    
    LoggerUtils.logDebug(
      'Task added: ${task.id} (${task.type.name})',
      tag: 'OfflineSyncManager',
    );

    // 자동 동기화 시작
    if (_status == SyncStatus.idle) {
      startSync();
    }

    return task.id;
  }

  /// 작업 제거
  Future<void> removeTask(String taskId) async {
    _pendingTasks.remove(taskId);
    await _deleteTaskFromDatabase(taskId);
    
    _updateProgress();
    
    LoggerUtils.logDebug(
      'Task removed: $taskId',
      tag: 'OfflineSyncManager',
    );
  }

  /// 동기화 시작
  Future<void> startSync() async {
    if (_status == SyncStatus.syncing) return;

    _status = SyncStatus.syncing;
    _progress = _progress.copyWith(
      status: SyncStatus.syncing,
      startTime: DateTime.now(),
    );
    
    onStatusChanged?.call(_status);
    _updateProgress();

    LoggerUtils.logDebug(
      'Sync started',
      tag: 'OfflineSyncManager',
    );

    try {
      await _processTasks();
    } catch (e) {
      _status = SyncStatus.error;
      _progress = _progress.copyWith(status: SyncStatus.error);
      
      onStatusChanged?.call(_status);
      _updateProgress();

      LoggerUtils.logError(
        'Sync failed: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 동기화 중지
  void stopSync() {
    if (_status != SyncStatus.syncing) return;

    _status = SyncStatus.idle;
    _progress = _progress.copyWith(status: SyncStatus.idle);
    
    onStatusChanged?.call(_status);
    _updateProgress();

    LoggerUtils.logDebug(
      'Sync stopped',
      tag: 'OfflineSyncManager',
    );
  }

  /// 동기화 일시 중지
  void pauseSync() {
    if (_status != SyncStatus.syncing) return;

    _status = SyncStatus.paused;
    _progress = _progress.copyWith(status: SyncStatus.paused);
    
    onStatusChanged?.call(_status);
    _updateProgress();

    LoggerUtils.logDebug(
      'Sync paused',
      tag: 'OfflineSyncManager',
    );
  }

  /// 작업 처리
  Future<void> _processTasks() async {
    final tasks = _pendingTasks.values.toList()
      ..sort((a, b) => b.complexity.compareTo(a.complexity));

    _progress = _progress.copyWith(
      totalTasks: tasks.length,
      pendingTasks: tasks.length,
    );
    _updateProgress();

    final semaphore = Semaphore(_maxConcurrentTasks);
    final futures = <Future<void>>[];

    for (final task in tasks) {
      futures.add(_processTaskWithSemaphore(task, semaphore));
    }

    await Future.wait(futures);

    _status = SyncStatus.completed;
    _progress = _progress.copyWith(status: SyncStatus.completed);
    
    onStatusChanged?.call(_status);
    _updateProgress();

    LoggerUtils.logDebug(
      'Sync completed: ${_progress.completedTasks} tasks processed',
      tag: 'OfflineSyncManager',
    );
  }

  /// 세마포어를 사용한 작업 처리
  Future<void> _processTaskWithSemaphore(OfflineTask task, Semaphore semaphore) async {
    await semaphore.acquire();
    
    try {
      await _processTask(task);
    } finally {
      semaphore.release();
    }
  }

  /// 개별 작업 처리
  Future<void> _processTask(OfflineTask task) async {
    _progress = _progress.copyWith(currentTask: task.id);
    _updateProgress();

    try {
      LoggerUtils.logDebug(
        'Processing task: ${task.id} (${task.type.name})',
        tag: 'OfflineSyncManager',
      );

      // 작업 타입별 처리
      switch (task.type) {
        case OfflineTaskType.create:
        case OfflineTaskType.insert:  // create와 insert는 같은 처리
          await _handleCreateTask(task);
          break;
        case OfflineTaskType.update:
          await _handleUpdateTask(task);
          break;
        case OfflineTaskType.delete:
          await _handleDeleteTask(task);
          break;
        case OfflineTaskType.sync:
          await _handleSyncTask(task);
          break;
        case OfflineTaskType.upload:
          await _handleUploadTask(task);
          break;
        case OfflineTaskType.download:
          await _handleDownloadTask(task);
          break;
      }

      // 성공 처리
      _pendingTasks.remove(task.id);
      _completedTasks[task.id] = task;
      await _updateTaskStatus(task.id, 'completed');

      _progress = _progress.copyWith(
        completedTasks: _progress.completedTasks + 1,
        pendingTasks: _progress.pendingTasks - 1,
      );
      _updateProgress();

      onTaskCompleted?.call(task);

      LoggerUtils.logDebug(
        'Task completed: ${task.id}',
        tag: 'OfflineSyncManager',
      );
    } catch (e) {
      // 실패 처리
      final updatedTask = OfflineTask(
        id: task.id,
        type: task.type,
        table: task.table,
        data: task.data,
        timestamp: task.timestamp,
        priority: task.priority,
        createdAt: task.createdAt,
        scheduledAt: task.scheduledAt,
        retryCount: task.retryCount + 1,
        errorMessage: e.toString(),
        metadata: task.metadata,
      );

      if (updatedTask.canRetry) {
        // 재시도 가능한 경우
        _pendingTasks[task.id] = updatedTask;
        await _updateTaskInDatabase(updatedTask);
        
        LoggerUtils.logWarning(
          'Task failed, will retry: ${task.id} (attempt ${updatedTask.retryCount})',
          tag: 'OfflineSyncManager',
        );
      } else {
        // 재시도 불가능한 경우
        _pendingTasks.remove(task.id);
        _failedTasks[task.id] = updatedTask;
        await _updateTaskStatus(task.id, 'failed');

        _progress = _progress.copyWith(
          failedTasks: _progress.failedTasks + 1,
          pendingTasks: _progress.pendingTasks - 1,
        );
        _updateProgress();

        onTaskFailed?.call(updatedTask, e.toString());

        LoggerUtils.logError(
          'Task failed permanently: ${task.id}',
          error: e,
          tag: 'OfflineSyncManager',
        );
      }
    }
  }

  /// CREATE 작업 처리
  Future<void> _handleCreateTask(OfflineTask task) async {
    await _database.insert(task.table, task.data);
  }

  /// UPDATE 작업 처리
  Future<void> _handleUpdateTask(OfflineTask task) async {
    final id = task.data['id'];
    await _database.update(
      task.table,
      task.data,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// DELETE 작업 처리
  Future<void> _handleDeleteTask(OfflineTask task) async {
    final id = task.data['id'];
    await _database.delete(
      task.table,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// SYNC 작업 처리
  Future<void> _handleSyncTask(OfflineTask task) async {
    // 서버와의 동기화 로직
    await Future.delayed(const Duration(seconds: 1)); // 시뮬레이션
  }

  /// UPLOAD 작업 처리
  Future<void> _handleUploadTask(OfflineTask task) async {
    // 파일 업로드 로직
    await Future.delayed(const Duration(seconds: 2)); // 시뮬레이션
  }

  /// DOWNLOAD 작업 처리
  Future<void> _handleDownloadTask(OfflineTask task) async {
    // 파일 다운로드 로직
    await Future.delayed(const Duration(seconds: 1)); // 시뮬레이션
  }

  /// 네트워크 연결 상태 리스너 설정
  Future<void> _setupConnectivityListener() async {
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
          final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
          _onConnectivityChanged(result);
        });
  }

  /// 네트워크 연결 상태 변경 처리
  void _onConnectivityChanged(ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      // 오프라인 상태
      pauseSync();
      LoggerUtils.logDebug(
        'Network disconnected, sync paused',
        tag: 'OfflineSyncManager',
      );
    } else {
      // 온라인 상태
      if (_status == SyncStatus.paused) {
        startSync();
        LoggerUtils.logDebug(
          'Network connected, sync resumed',
          tag: 'OfflineSyncManager',
        );
      }
    }
  }

  /// 자동 동기화 시작
  void startAutoSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_syncInterval, (_) {
      if (_status == SyncStatus.idle) {
        startSync();
      }
    });

    LoggerUtils.logDebug(
      'Auto sync started (interval: ${_syncInterval.inMinutes} minutes)',
      tag: 'OfflineSyncManager',
    );
  }

  /// 자동 동기화 중지
  void stopAutoSync() {
    _syncTimer?.cancel();
    _syncTimer = null;

    LoggerUtils.logDebug(
      'Auto sync stopped',
      tag: 'OfflineSyncManager',
    );
  }

  /// 정리 타이머 시작
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) => _cleanup());
  }

  /// 정리 작업
  void _cleanup() {
    final now = DateTime.now();
    final expiredTasks = <String>[];

    // 완료된 작업 중 오래된 것들 정리 (24시간 이상)
    for (final entry in _completedTasks.entries) {
      if (now.difference(entry.value.createdAt) > const Duration(hours: 24)) {
        expiredTasks.add(entry.key);
      }
    }

    // 실패한 작업 중 오래된 것들 정리 (7일 이상)
    for (final entry in _failedTasks.entries) {
      if (now.difference(entry.value.createdAt) > const Duration(days: 7)) {
        expiredTasks.add(entry.key);
      }
    }

    for (final taskId in expiredTasks) {
      _completedTasks.remove(taskId);
      _failedTasks.remove(taskId);
    }

    if (expiredTasks.isNotEmpty) {
      LoggerUtils.logDebug(
        'Cleaned up ${expiredTasks.length} expired tasks',
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 진행률 업데이트
  void _updateProgress() {
    onProgressChanged?.call(_progress);
  }

  /// 데이터베이스에 작업 저장
  Future<void> _saveTaskToDatabase(OfflineTask task) async {
    try {
      await _database.insert('offline_tasks', {
        'id': task.id,
        'data': jsonEncode(task.toJson()),
        'status': 'pending',
        'created_at': task.createdAt.toIso8601String(),
      });
    } catch (e) {
      LoggerUtils.logError(
        'Failed to save task to database: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 데이터베이스에서 작업 삭제
  Future<void> _deleteTaskFromDatabase(String taskId) async {
    try {
      await _database.delete(
        'offline_tasks',
        where: 'id = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to delete task from database: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 데이터베이스에서 작업 상태 업데이트
  Future<void> _updateTaskStatus(String taskId, String status) async {
    try {
      await _database.update(
        'offline_tasks',
        {'status': status},
        where: 'id = ?',
        whereArgs: [taskId],
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to update task status: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 데이터베이스에서 작업 업데이트
  Future<void> _updateTaskInDatabase(OfflineTask task) async {
    try {
      await _database.update(
        'offline_tasks',
        {'data': jsonEncode(task.toJson())},
        where: 'id = ?',
        whereArgs: [task.id],
      );
    } catch (e) {
      LoggerUtils.logError(
        'Failed to update task in database: $e',
        error: e,
        tag: 'OfflineSyncManager',
      );
    }
  }

  /// 통계 정보
  Map<String, dynamic> getStats() {
    return {
      'status': _status.name,
      'pendingTasks': _pendingTasks.length,
      'completedTasks': _completedTasks.length,
      'failedTasks': _failedTasks.length,
      'progress': _progress.progress,
      'successRate': _progress.successRate,
      'autoSyncActive': _syncTimer?.isActive ?? false,
    };
  }

  /// 리소스 정리
  void dispose() {
    stopAutoSync();
    _cleanupTimer?.cancel();
    _connectivitySubscription?.cancel();
    
    _pendingTasks.clear();
    _completedTasks.clear();
    _failedTasks.clear();
  }
}

/// 세마포어 구현
class Semaphore {
  int _currentPermits;
  final Queue<Completer<void>> _waiting = Queue();

  Semaphore(int maxPermits) : _currentPermits = maxPermits;

  /// 허가 획득
  Future<void> acquire() async {
    if (_currentPermits > 0) {
      _currentPermits--;
      return;
    }

    final completer = Completer<void>();
    _waiting.add(completer);
    await completer.future;
  }

  /// 허가 해제
  void release() {
    if (_waiting.isNotEmpty) {
      final completer = _waiting.removeFirst();
      completer.complete();
    } else {
      _currentPermits++;
    }
  }

  /// 현재 허가 수
  int get availablePermits => _currentPermits;

  /// 대기 중인 작업 수
  int get waitingCount => _waiting.length;
}

/// 동기화 전략
class SyncStrategy {
  final bool enableAutoSync;
  final Duration syncInterval;
  final int maxConcurrentTasks;
  final int maxRetries;
  final bool syncOnNetworkChange;
  final bool syncOnAppForeground;
  final List<String> priorityTables;

  const SyncStrategy({
    this.enableAutoSync = true,
    this.syncInterval = const Duration(minutes: 5),
    this.maxConcurrentTasks = 3,
    this.maxRetries = 3,
    this.syncOnNetworkChange = true,
    this.syncOnAppForeground = true,
    this.priorityTables = const [],
  });

  /// 배터리 효율성 고려 전략
  factory SyncStrategy.batteryOptimized() {
    return const SyncStrategy(
      enableAutoSync: true,
      syncInterval: Duration(minutes: 15),
      maxConcurrentTasks: 2,
      maxRetries: 2,
      syncOnNetworkChange: true,
      syncOnAppForeground: true,
      priorityTables: ['sales_log', 'products'],
    );
  }

  /// 성능 최적화 전략
  factory SyncStrategy.performanceOptimized() {
    return const SyncStrategy(
      enableAutoSync: true,
      syncInterval: Duration(minutes: 2),
      maxConcurrentTasks: 5,
      maxRetries: 5,
      syncOnNetworkChange: true,
      syncOnAppForeground: true,
      priorityTables: [],
    );
  }
} 