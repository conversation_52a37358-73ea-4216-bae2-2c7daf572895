# 실시간 동기화 시스템 - 개발자 가이드

## 🏗️ 아키텍처 개요

파라바라 앱의 실시간 동기화 시스템은 Flutter + Riverpod + Firebase Firestore를 기반으로 구축되었습니다.

### 핵심 구성요소

```
┌─────────────────────────────────────────────────────────┐
│                   Application Layer                     │
├─────────────────────────────────────────────────────────┤
│ Widgets: RealtimeSyncInitializer, EventSyncController   │
├─────────────────────────────────────────────────────────┤
│ Providers: realtime_sync_provider.dart                  │
├─────────────────────────────────────────────────────────┤
│ Services: RealtimeSyncService                           │
├─────────────────────────────────────────────────────────┤
│ Repositories: RealtimeProductRepository, etc.           │
├─────────────────────────────────────────────────────────┤
│ Models: SyncMetadata, Product (enhanced)               │
├─────────────────────────────────────────────────────────┤
│ Database: SQLite + Firestore                           │
└─────────────────────────────────────────────────────────┘
```

## 📁 파일 구조

```
lib/
├── models/
│   └── sync_metadata.dart              # 동기화 메타데이터 모델
├── services/
│   └── realtime_sync_service.dart      # 핵심 동기화 서비스
├── repositories/
│   └── realtime_repositories.dart      # 실시간 저장소 확장
├── providers/
│   └── realtime_sync_provider.dart     # Riverpod 상태 관리
├── utils/
│   ├── realtime_sync_migration.dart    # DB 마이그레이션
│   └── sync_migration_manager.dart     # 마이그레이션 관리
└── widgets/
    └── realtime_sync_widgets.dart      # UI 컴포넌트
```

## 🔧 핵심 클래스

### 1. SyncMetadata
```dart
@freezed
class SyncMetadata with _$SyncMetadata {
  const factory SyncMetadata({
    required String id,
    required DateTime lastModified,
    required String deviceId,
    required SyncStatus status,
    ConflictResolution? conflictResolution,
  }) = _SyncMetadata;
}
```
- **목적**: 각 데이터 항목의 동기화 상태를 추적
- **핵심 필드**: 
  - `lastModified`: 마지막 수정 시간 (충돌 해결 기준)
  - `deviceId`: 수정한 디바이스 식별자
  - `status`: 동기화 상태 (pending, synced, conflict)

### 2. RealtimeSyncService
```dart
class RealtimeSyncService {
  Future<void> subscribeToEvent(int eventId);
  Future<void> unsubscribeFromEvent(int eventId);
  Future<void> syncToServer<T>(T data, String collection);
  Future<T?> mergeConflict<T>(T local, T remote);
}
```
- **목적**: Firestore와의 실시간 통신 관리
- **핵심 기능**:
  - 이벤트별 구독 관리
  - 오프라인 큐 처리
  - 충돌 해결
  - 배치 동기화

### 3. RealtimeProductRepository
```dart
class RealtimeProductRepository extends ProductRepository {
  @override
  Future<int> insertProduct(Product product);
  
  @override
  Future<void> updateProduct(Product product);
  
  Future<Product> mergeFromServer(Product serverProduct);
}
```
- **목적**: 기존 Repository 패턴에 실시간 동기화 추가
- **특징**: 기존 API 호환성 유지하면서 실시간 기능 확장

## 🔄 동기화 플로우

### 1. 데이터 생성/수정 플로우
```
User Action → Repository → Local DB Update → Sync Service → Firestore
                      ↓
                 UI Update (Optimistic)
```

### 2. 실시간 수신 플로우
```
Firestore Change → Sync Service → Repository → Local DB → Provider → UI
```

### 3. 충돌 해결 플로우
```
Conflict Detected → Compare Timestamps → Merge Strategy → Update Both Sides
```

## 📊 데이터 모델 확장

### 기존 Product 모델 확장
```dart
@freezed
class Product with _$Product {
  const factory Product({
    required int id,
    required String name,
    required double price,
    // ... 기존 필드들
    SyncMetadata? syncMetadata,  // 🆕 추가된 필드
  }) = _Product;
  
  // 🆕 동기화 헬퍼 메서드들
  Product withSyncUpdate(String deviceId);
  Product withSyncMetadata(SyncMetadata metadata);
  Map<String, dynamic> toSyncMap();
}
```

### SQLite 스키마 확장
```sql
-- 기존 테이블에 동기화 컬럼 추가
ALTER TABLE products ADD COLUMN sync_id TEXT;
ALTER TABLE products ADD COLUMN sync_last_modified INTEGER;
ALTER TABLE products ADD COLUMN sync_device_id TEXT;
ALTER TABLE products ADD COLUMN sync_status TEXT DEFAULT 'pending';
```

## 🚀 마이그레이션 시스템

### 자동 마이그레이션 프로세스
```dart
class SyncMigrationManager {
  static Future<void> performMigration() async {
    // 1. 현재 마이그레이션 상태 확인
    if (await isMigrationComplete()) return;
    
    // 2. 데이터베이스 스키마 업데이트
    await RealtimeSyncMigration.migrate();
    
    // 3. 기존 데이터에 동기화 메타데이터 추가
    await _initializeSyncMetadata();
    
    // 4. 마지막 수동 동기화 실행
    await _performFinalManualSync();
    
    // 5. 실시간 동기화 활성화
    await _enableRealtimeSync();
    
    // 6. 마이그레이션 완료 표시
    await _markMigrationComplete();
  }
}
```

## 🔌 Riverpod 통합

### Provider 계층 구조
```dart
// 디바이스 ID 생성
final deviceIdProvider = FutureProvider<String>((ref) async { ... });

// 실시간 동기화 서비스
final realtimeSyncServiceProvider = Provider<RealtimeSyncService>((ref) { ... });

// 동기화 상태 관리
final syncStateProvider = StateNotifierProvider<SyncStateNotifier, SyncStateData>((ref) { ... });

// 이벤트별 동기화 제어
final eventSyncProvider = StateNotifierProvider.family<EventSyncNotifier, EventSyncState, int>((ref, eventId) { ... });
```

### 상태 관리 패턴
```dart
class SyncStateNotifier extends StateNotifier<SyncStateData> {
  void updateSyncState(SyncState newState) {
    state = state.copyWith(
      state: newState,
      lastSync: DateTime.now(),
    );
  }
  
  Future<void> handleSyncError(String error) async {
    state = state.copyWith(
      state: SyncState.error,
      error: error,
    );
  }
}
```

## 🛡️ 오류 처리 및 복원력

### 네트워크 오류 처리
```dart
class OfflineQueue {
  final List<SyncOperation> _pendingOperations = [];
  
  Future<void> addOperation(SyncOperation operation) async {
    _pendingOperations.add(operation);
    await _persistQueue(); // SQLite에 저장
  }
  
  Future<void> processPendingOperations() async {
    for (final operation in _pendingOperations) {
      try {
        await operation.execute();
        _pendingOperations.remove(operation);
      } catch (e) {
        // 재시도 로직
      }
    }
  }
}
```

### 충돌 해결 전략
```dart
Future<T> mergeConflict<T>(T local, T remote) async {
  if (local.syncMetadata.lastModified.isAfter(remote.syncMetadata.lastModified)) {
    return local; // 로컬이 더 최신
  } else {
    return remote; // 서버가 더 최신
  }
}
```

## 📱 UI 통합

### 실시간 동기화 상태 표시
```dart
class RealtimeSyncStatusWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncState = ref.watch(syncStateProvider);
    
    return Icon(
      _getIconForState(syncState.state),
      color: _getColorForState(syncState.state),
    );
  }
}
```

### 앱 시작시 초기화
```dart
class RealtimeSyncInitializer extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initAsync = ref.watch(realtimeSyncInitProvider);
    
    return initAsync.when(
      data: (success) => success ? child : errorWidget,
      loading: () => loadingWidget,
      error: (error, _) => errorWidget,
    );
  }
}
```

## 🔍 디버깅 및 로깅

### 로그 시스템
```dart
class LoggerUtils {
  static void logSyncOperation(String operation, {
    String? tag,
    Map<String, dynamic>? data,
  }) {
    // 개발 모드에서만 상세 로그
    if (kDebugMode) {
      print('[SYNC] $tag: $operation');
      if (data != null) print('Data: $data');
    }
  }
}
```

### 동기화 통계 수집
```dart
class SyncMetrics {
  static int totalSyncOperations = 0;
  static int failedOperations = 0;
  static Duration averageSyncTime = Duration.zero;
  
  static void recordSyncOperation(Duration duration, bool success) {
    totalSyncOperations++;
    if (!success) failedOperations++;
    // 평균 계산 로직...
  }
}
```

## 🚀 성능 최적화

### 배치 처리
```dart
class BatchSyncProcessor {
  final List<SyncOperation> _batch = [];
  Timer? _batchTimer;
  
  void addToBatch(SyncOperation operation) {
    _batch.add(operation);
    
    // 100ms 후 배치 처리 (디바운싱)
    _batchTimer?.cancel();
    _batchTimer = Timer(Duration(milliseconds: 100), () {
      _processBatch();
    });
  }
  
  Future<void> _processBatch() async {
    if (_batch.isEmpty) return;
    
    // Firestore 배치 쓰기 사용
    final batch = FirebaseFirestore.instance.batch();
    for (final operation in _batch) {
      operation.addToBatch(batch);
    }
    await batch.commit();
    
    _batch.clear();
  }
}
```

### 델타 동기화
```dart
Future<void> syncDelta(DateTime lastSyncTime) async {
  // 마지막 동기화 이후 변경된 항목만 조회
  final changes = await FirebaseFirestore.instance
      .collection('products')
      .where('lastModified', isGreaterThan: lastSyncTime.toIso8601String())
      .get();
  
  for (final doc in changes.docs) {
    await _processChange(doc);
  }
}
```

## 🧪 테스트 전략

### 단위 테스트
```dart
testWidgets('RealtimeSyncService handles conflicts correctly', (tester) async {
  final service = RealtimeSyncService();
  
  final localProduct = Product(name: 'Local', lastModified: DateTime.now());
  final remoteProduct = Product(name: 'Remote', lastModified: DateTime.now().subtract(Duration(minutes: 1)));
  
  final merged = await service.mergeConflict(localProduct, remoteProduct);
  
  expect(merged.name, equals('Local')); // 로컬이 더 최신이므로
});
```

### 통합 테스트
```dart
testWidgets('End-to-end sync flow', (tester) async {
  // 1. 로컬에서 제품 생성
  await tester.tap(find.byKey(Key('add_product')));
  
  // 2. 동기화 대기
  await tester.pump(Duration(seconds: 2));
  
  // 3. 다른 디바이스에서 확인 (모킹)
  verify(mockFirestore.collection('products').add(any)).called(1);
});
```

## 📈 모니터링 및 메트릭

### Firebase Performance Monitoring
```dart
final trace = FirebasePerformance.instance.newTrace('realtime_sync');
await trace.start();
try {
  await syncOperation();
} finally {
  await trace.stop();
}
```

### 커스텀 메트릭
```dart
class SyncAnalytics {
  static void trackSyncSuccess(String collection, Duration duration) {
    FirebaseAnalytics.instance.logEvent(
      name: 'sync_success',
      parameters: {
        'collection': collection,
        'duration_ms': duration.inMilliseconds,
      },
    );
  }
}
```

## 🔐 보안 고려사항

### Firestore 보안 규칙
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 사용자는 자신의 데이터만 접근 가능
    match /events/{eventId}/{collection}/{document} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

### 데이터 검증
```dart
bool isValidSyncData(Map<String, dynamic> data) {
  return data.containsKey('syncMetadata') &&
         data['syncMetadata']['deviceId'] != null &&
         data['syncMetadata']['lastModified'] != null;
}
```

## 📋 체크리스트 (배포 전)

### 기능 검증
- [ ] 실시간 동기화 작동 확인
- [ ] 오프라인 모드 테스트
- [ ] 충돌 해결 시나리오 테스트
- [ ] 마이그레이션 프로세스 검증
- [ ] 다중 디바이스 동기화 테스트

### 성능 검증
- [ ] 배치 처리 최적화 확인
- [ ] 메모리 사용량 모니터링
- [ ] 네트워크 사용량 최적화
- [ ] 배터리 사용량 측정

### 오류 처리
- [ ] 네트워크 오류 시나리오
- [ ] Firestore 한도 초과 시나리오
- [ ] 데이터 손상 복구 테스트

## 🚀 향후 개선 계획

### 단기 (1-2개월)
- [ ] 수동 동기화 옵션 추가
- [ ] 고급 설정 UI 구현
- [ ] 동기화 통계 대시보드

### 중기 (3-6개월)
- [ ] 실시간 알림 시스템
- [ ] 협업 기능 (동시 편집 표시)
- [ ] 고급 충돌 해결 UI

### 장기 (6개월+)
- [ ] WebRTC 기반 P2P 동기화
- [ ] GraphQL 실시간 구독
- [ ] AI 기반 충돌 해결

---

> 💡 **개발팁**: 실시간 동기화는 복잡한 시스템입니다. 단계적으로 구현하고 충분한 테스트를 거쳐 안정성을 확보하세요.

---

*마지막 업데이트: 2025년 7월*
