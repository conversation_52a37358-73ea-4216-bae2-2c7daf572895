/// 실시간 동기화를 위한 데이터베이스 마이그레이션
/// 
/// 기존 테이블에 동기화 메타데이터 컬럼을 추가하는 마이그레이션입니다.
/// 기존 데이터는 그대로 보존되며, 새로운 동기화 기능만 추가됩니다.
/// 
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'package:sqflite/sqflite.dart';
import '../utils/logger_utils.dart';

/// 실시간 동기화 마이그레이션 클래스
class RealtimeSyncMigration {
  static const String _tag = 'RealtimeSyncMigration';
  
  /// 현재 데이터베이스 버전
  static const int currentVersion = 2;
  
  /// 이전 버전 (동기화 기능 추가 전)
  static const int previousVersion = 1;

  /// 데이터베이스 마이그레이션 실행
  static Future<void> migrate(Database db, int oldVersion, int newVersion) async {
    LoggerUtils.logInfo('데이터베이스 마이그레이션 시작: $oldVersion -> $newVersion', tag: _tag);
    
    try {
      if (oldVersion < 2 && newVersion >= 2) {
        await _addSyncMetadataColumns(db);
      }
      
      LoggerUtils.logInfo('데이터베이스 마이그레이션 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('데이터베이스 마이그레이션 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 동기화 메타데이터 컬럼 추가
  static Future<void> _addSyncMetadataColumns(Database db) async {
    LoggerUtils.logInfo('동기화 메타데이터 컬럼 추가 시작', tag: _tag);
    
    try {
      // 각 테이블에 syncMetadata 컬럼 추가
      final tables = ['products', 'sellers', 'sales_logs', 'prepayments'];
      
      for (final table in tables) {
        try {
          // 컬럼이 이미 존재하는지 확인
          final result = await db.rawQuery("PRAGMA table_info($table)");
          final hasMetadataColumn = result.any((column) => column['name'] == 'syncMetadata');
          
          if (!hasMetadataColumn) {
            await db.execute('''
              ALTER TABLE $table 
              ADD COLUMN syncMetadata TEXT
            ''');
            LoggerUtils.logInfo('$table 테이블에 syncMetadata 컬럼 추가 완료', tag: _tag);
          } else {
            LoggerUtils.logInfo('$table 테이블에 syncMetadata 컬럼이 이미 존재함', tag: _tag);
          }
        } catch (e) {
          LoggerUtils.logError('$table 테이블 컬럼 추가 실패', tag: _tag, error: e);
          // 개별 테이블 실패해도 계속 진행
        }
      }
      
      LoggerUtils.logInfo('동기화 메타데이터 컬럼 추가 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('동기화 메타데이터 컬럼 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  // 인덱스 생성 함수는 DatabaseService에서 처리하므로 제거됨

  /// 기존 데이터 동기화 메타데이터 초기화
  static Future<void> initializeExistingData(Database db) async {
    LoggerUtils.logInfo('기존 데이터 동기화 메타데이터 초기화 시작', tag: _tag);
    
    try {
      final tables = ['products', 'sellers', 'sales_logs', 'prepayments'];
      
      for (final table in tables) {
        try {
          // syncMetadata가 null인 기존 데이터들에 기본 메타데이터 설정
          final count = await db.rawUpdate('''
            UPDATE $table 
            SET syncMetadata = ? 
            WHERE syncMetadata IS NULL
          ''', [_getDefaultSyncMetadata()]);
          
          LoggerUtils.logInfo('$table 테이블 $count개 레코드 메타데이터 초기화 완료', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('$table 테이블 메타데이터 초기화 실패', tag: _tag, error: e);
        }
      }
      
      LoggerUtils.logInfo('기존 데이터 동기화 메타데이터 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('기존 데이터 메타데이터 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 기본 동기화 메타데이터 JSON 문자열 생성
  static String _getDefaultSyncMetadata() {
    final now = DateTime.now().toUtc();
    return '''{
      "lastModified": "${now.toIso8601String()}",
      "version": 1,
      "syncStatus": "synced",
      "deviceId": "migration_init",
      "serverTimestamp": null,
      "checksum": null,
      "conflictData": null
    }''';
  }

  /// 마이그레이션 검증
  static Future<bool> verifyMigration(Database db) async {
    LoggerUtils.logInfo('마이그레이션 검증 시작', tag: _tag);
    
    try {
      final tables = ['products', 'sellers', 'sales_logs', 'prepayments'];
      
      for (final table in tables) {
        // 테이블 구조 확인
        final result = await db.rawQuery("PRAGMA table_info($table)");
        final hasMetadataColumn = result.any((column) => column['name'] == 'syncMetadata');
        
        if (!hasMetadataColumn) {
          LoggerUtils.logError('$table 테이블에 syncMetadata 컬럼이 없음', tag: _tag);
          return false;
        }
      }
      
      LoggerUtils.logInfo('마이그레이션 검증 완료', tag: _tag);
      return true;
    } catch (e) {
      LoggerUtils.logError('마이그레이션 검증 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 롤백 (필요시)
  static Future<void> rollback(Database db) async {
    LoggerUtils.logWarning('마이그레이션 롤백 시작', tag: _tag);
    
    try {
      // SQLite는 컬럼 삭제를 직접 지원하지 않으므로
      // 테이블 재생성이 필요하지만, 데이터 손실 위험이 있어
      // 실제로는 컬럼만 NULL로 설정
      final tables = ['products', 'sellers', 'sales_logs', 'prepayments'];
      
      for (final table in tables) {
        try {
          await db.rawUpdate('''
            UPDATE $table 
            SET syncMetadata = NULL
          ''');
          LoggerUtils.logInfo('$table 테이블 syncMetadata 초기화 완료', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('$table 테이블 롤백 실패', tag: _tag, error: e);
        }
      }
      
      LoggerUtils.logWarning('마이그레이션 롤백 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('마이그레이션 롤백 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
