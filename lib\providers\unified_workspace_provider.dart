import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_workspace.dart';
import '../models/seller.dart';
import '../services/event_workspace_manager.dart';
import '../services/workspace_data_manager.dart';
import '../utils/logger_utils.dart';
import 'nickname_provider.dart';
import 'seller_provider.dart';
import 'data_sync_provider.dart';

/// 통합 워크스페이스 상태
class UnifiedWorkspaceState {
  final List<EventWorkspace> workspaces;
  final EventWorkspace? currentWorkspace;
  final bool isLoading;
  final bool isDataLoading;
  final String? errorMessage;
  final String? loadingMessage; // 진행 상황 메시지 추가

  const UnifiedWorkspaceState({
    this.workspaces = const [],
    this.currentWorkspace,
    this.isLoading = false,
    this.isDataLoading = false,
    this.errorMessage,
    this.loadingMessage,
  });

  UnifiedWorkspaceState copyWith({
    List<EventWorkspace>? workspaces,
    EventWorkspace? currentWorkspace,
    bool? isLoading,
    bool? isDataLoading,
    String? errorMessage,
    String? loadingMessage,
  }) {
    return UnifiedWorkspaceState(
      workspaces: workspaces ?? this.workspaces,
      currentWorkspace: currentWorkspace ?? this.currentWorkspace,
      isLoading: isLoading ?? this.isLoading,
      isDataLoading: isDataLoading ?? this.isDataLoading,
      errorMessage: errorMessage,
      loadingMessage: loadingMessage,
    );
  }

  /// 현재 워크스페이스가 설정되어 있는지 확인
  bool get hasCurrentWorkspace => currentWorkspace != null;

  /// 워크스페이스가 존재하는지 확인
  bool get hasWorkspaces => workspaces.isNotEmpty;

  /// 진행중인 행사 워크스페이스 목록
  List<EventWorkspace> get ongoingWorkspaces {
    return workspaces.where((w) => w.status == WorkspaceStatus.ongoing).toList();
  }

  /// 예정된 행사 워크스페이스 목록
  List<EventWorkspace> get upcomingWorkspaces {
    return workspaces.where((w) => w.status == WorkspaceStatus.upcoming).toList();
  }

  /// 완료된 행사 워크스페이스 목록
  List<EventWorkspace> get completedWorkspaces {
    return workspaces.where((w) => w.status == WorkspaceStatus.completed).toList();
  }
}

/// 통합 워크스페이스 관리 Provider
/// 
/// 기존의 복잡한 워크스페이스 시스템을 단순화하고,
/// 데이터 동기화를 중앙집중식으로 관리합니다.
class UnifiedWorkspaceNotifier extends StateNotifier<UnifiedWorkspaceState> {
  static const String _tag = 'UnifiedWorkspaceNotifier';

  final EventWorkspaceManager _workspaceManager = EventWorkspaceManager.instance;
  final WorkspaceDataManager _dataManager = WorkspaceDataManager.instance;
  final Ref _ref;

  UnifiedWorkspaceNotifier(this._ref) : super(const UnifiedWorkspaceState()) {
    _initialize();
  }

  /// 초기화
  Future<void> _initialize() async {
    try {
      LoggerUtils.methodStart('_initialize', tag: _tag);
      
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      // 워크스페이스 매니저 초기화
      await _workspaceManager.initialize();
      
      // 상태 업데이트
      _updateStateFromManager();
      
      // 워크스페이스 매니저 리스너 등록
      _workspaceManager.addListener(_updateStateFromManager);
      
      // 데이터 관리자 리스너 등록
      _dataManager.addListener(_updateDataLoadingState);
      
      state = state.copyWith(isLoading: false);
      
      LoggerUtils.logInfo('통합 워크스페이스 Provider 초기화 완료', tag: _tag);
      LoggerUtils.methodEnd('_initialize', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('통합 워크스페이스 Provider 초기화 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        errorMessage: '워크스페이스 초기화에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 새 행사 워크스페이스 생성
  Future<EventWorkspace?> createWorkspace({
    required String name,
    required DateTime startDate,
    required DateTime endDate,
    bool setAsCurrent = true,
    String? imagePath,
    String? description,
  }) async {
    try {
      LoggerUtils.methodStart('createWorkspace', tag: _tag, data: {'name': name});

      state = state.copyWith(isLoading: true, errorMessage: null);

      final workspace = await _workspaceManager.createWorkspace(
        name: name,
        startDate: startDate,
        endDate: endDate,
        setAsCurrent: setAsCurrent,
        imagePath: imagePath,
        description: description,
      );

      // 워크스페이스 매니저 상태를 즉시 반영
      _updateStateFromManager();

      // 새로 생성된 행사를 Firebase에 즉시 업로드 (온보딩 동기화 문제 해결)
      try {
        final dataSyncService = _ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleEvent(workspace.toEvent());
        LoggerUtils.logInfo('온보딩 행사 Firebase 업로드 완료: ${workspace.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('온보딩 행사 Firebase 업로드 실패: ${workspace.name}', tag: _tag, error: e);
        // Firebase 업로드 실패해도 로컬 워크스페이스 생성은 유지
      }

      // 현재 워크스페이스로 설정된 경우 데이터 동기화
      if (setAsCurrent) {
        await _dataManager.switchWorkspace(workspace, _ref);
      } else {
        // 현재 워크스페이스로 설정하지 않더라도 닉네임 기반 판매자는 생성
        await _createNicknameBasedSellerForWorkspace(workspace);
      }

      state = state.copyWith(isLoading: false);
      
      LoggerUtils.logInfo('행사 워크스페이스 생성 완료: ${workspace.name}', tag: _tag);
      LoggerUtils.methodEnd('createWorkspace', tag: _tag);

      return workspace;
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 생성 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 워크스페이스 생성에 실패했습니다: ${e.toString()}',
      );
      return null;
    }
  }

  /// 행사 워크스페이스 전환
  Future<void> switchToWorkspace(EventWorkspace workspace) async {
    try {
      LoggerUtils.methodStart('switchToWorkspace', tag: _tag, data: {'workspaceId': workspace.id, 'name': workspace.name});

      if (state.currentWorkspace?.id == workspace.id) {
        LoggerUtils.logInfo('이미 현재 워크스페이스입니다: ${workspace.name}', tag: _tag);
        return;
      }

      // 1단계: 워크스페이스 전환 시작
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
        loadingMessage: '행사 전환 중...'
      );

      // 2단계: 워크스페이스 매니저 전환
      state = state.copyWith(loadingMessage: '워크스페이스 설정 중...');
      await _workspaceManager.switchToWorkspace(workspace);

      // 3단계: 데이터 동기화
      state = state.copyWith(loadingMessage: '데이터 동기화 중...');
      await _dataManager.switchWorkspace(workspace, _ref);

      // 4단계: 상태 업데이트
      state = state.copyWith(loadingMessage: '화면 업데이트 중...');
      _updateStateFromManager();

      // 완료
      state = state.copyWith(
        isLoading: false,
        loadingMessage: null
      );

      LoggerUtils.logInfo('행사 워크스페이스 전환 완료: ${workspace.name}', tag: _tag);
      LoggerUtils.methodEnd('switchToWorkspace', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('워크스페이스 전환 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        loadingMessage: null,
        errorMessage: '행사 워크스페이스 전환에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 행사 워크스페이스 삭제
  Future<void> deleteWorkspace(EventWorkspace workspace) async {
    try {
      LoggerUtils.methodStart('deleteWorkspace', tag: _tag, data: {'workspaceId': workspace.id, 'name': workspace.name});
      
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      await _workspaceManager.deleteWorkspace(workspace);

      // 워크스페이스 매니저 상태를 즉시 반영
      _updateStateFromManager();

      // 현재 워크스페이스가 삭제된 경우 데이터 동기화
      if (state.currentWorkspace?.id == workspace.id) {
        await _dataManager.switchWorkspace(state.currentWorkspace, _ref);
      }

      state = state.copyWith(isLoading: false);
      
      LoggerUtils.logInfo('행사 워크스페이스 삭제 완료: ${workspace.name}', tag: _tag);
      LoggerUtils.methodEnd('deleteWorkspace', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('행사 워크스페이스 삭제 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        errorMessage: '행사 워크스페이스 삭제에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 새로고침
  Future<void> refresh() async {
    try {
      LoggerUtils.methodStart('refresh', tag: _tag);
      
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      // Firebase 로그인 상태 확인 (데이터 동기화는 AppWrapper에서 수행)
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        LoggerUtils.logInfo('Firebase 사용자 확인됨 - 로컬 데이터 로드만 수행', tag: _tag);
        // 중복 동기화 방지: 실제 데이터 동기화는 AppWrapper._performDataSync()에서 수행
        LoggerUtils.logInfo('데이터 동기화는 AppWrapper에서 수행됩니다', tag: _tag);
      }
      
      // 로컬 워크스페이스 데이터 다시 로드
      await _initialize();
      
      LoggerUtils.methodEnd('refresh', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('워크스페이스 새로고침 실패', tag: _tag, error: e, stackTrace: stackTrace);
      state = state.copyWith(
        isLoading: false,
        errorMessage: '워크스페이스 새로고침에 실패했습니다: ${e.toString()}',
      );
    }
  }

  /// 에러 클리어
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 워크스페이스 매니저로부터 상태 업데이트
  void _updateStateFromManager() {
    state = state.copyWith(
      workspaces: _workspaceManager.workspaces,
      currentWorkspace: _workspaceManager.currentWorkspace,
    );
  }

  /// 데이터 로딩 상태 업데이트
  void _updateDataLoadingState() {
    state = state.copyWith(
      isDataLoading: _dataManager.isLoading,
    );
  }

  @override
  void dispose() {
    _workspaceManager.removeListener(_updateStateFromManager);
    _dataManager.removeListener(_updateDataLoadingState);
    super.dispose();
  }

  /// 특정 워크스페이스에 닉네임 기반 판매자 생성
  Future<void> _createNicknameBasedSellerForWorkspace(EventWorkspace workspace) async {
    try {
      LoggerUtils.logInfo('워크스페이스에 닉네임 기반 판매자 자동 생성 시작: ${workspace.name} (ID: ${workspace.id})', tag: _tag);
      
      // 닉네임 정보 가져오기
      final nickname = _ref.read(nicknameProvider);
      if (nickname == null) {
        LoggerUtils.logInfo('닉네임이 없어 판매자 자동 생성을 건너뜁니다', tag: _tag);
        return;
      }

      // 이미 해당 워크스페이스에 닉네임 기반 판매자가 있는지 확인
      final sellerRepository = _ref.read(sellerRepositoryProvider);
      final existingSellers = await sellerRepository.getSellersByEventId(workspace.id);
      final nicknameBasedSeller = existingSellers.where((seller) => seller.name == nickname.name).firstOrNull;

      if (nicknameBasedSeller != null) {
        LoggerUtils.logInfo('이미 닉네임 기반 판매자가 존재합니다: ${nickname.name}', tag: _tag);
        return;
      }

      // 닉네임 기반 판매자 생성
      final newSeller = Seller.create(
        name: nickname.name,
        isDefault: true,
        eventId: workspace.id,
      );

      // 판매자 저장
      final sellerId = await sellerRepository.insertSeller(newSeller);
      
      LoggerUtils.logInfo('닉네임 기반 판매자 자동 생성 완료: ${nickname.name} (ID: $sellerId)', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '닉네임 기반 판매자 자동 생성 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'workspaceId': workspace.id, 'workspaceName': workspace.name},
      );
    }
  }
}

/// 통합 워크스페이스 Provider
final unifiedWorkspaceProvider = StateNotifierProvider<UnifiedWorkspaceNotifier, UnifiedWorkspaceState>((ref) {
  return UnifiedWorkspaceNotifier(ref);
});

/// 현재 워크스페이스만 필요한 경우를 위한 Provider
final currentWorkspaceProvider = Provider<EventWorkspace?>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.currentWorkspace;
});

/// 현재 워크스페이스 ID만 필요한 경우를 위한 Provider
final currentWorkspaceIdProvider = Provider<int?>((ref) {
  final currentWorkspace = ref.watch(currentWorkspaceProvider);
  return currentWorkspace?.id;
});

/// 현재 워크스페이스 이름만 필요한 경우를 위한 Provider
final currentWorkspaceNameProvider = Provider<String>((ref) {
  final currentWorkspace = ref.watch(currentWorkspaceProvider);
  return currentWorkspace?.name ?? '행사 워크스페이스 없음';
});

/// 워크스페이스가 설정되어 있는지 확인하는 Provider
final hasCurrentWorkspaceProvider = Provider<bool>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.hasCurrentWorkspace;
});

/// 행사 워크스페이스 목록만 필요한 경우를 위한 Provider
final workspaceListProvider = Provider<List<EventWorkspace>>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.workspaces;
});

/// 진행중인 행사 워크스페이스 목록 Provider
final ongoingWorkspacesProvider = Provider<List<EventWorkspace>>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.ongoingWorkspaces;
});

/// 예정된 행사 워크스페이스 목록 Provider
final upcomingWorkspacesProvider = Provider<List<EventWorkspace>>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.upcomingWorkspaces;
});

/// 완료된 행사 워크스페이스 목록 Provider
final completedWorkspacesProvider = Provider<List<EventWorkspace>>((ref) {
  final workspaceState = ref.watch(unifiedWorkspaceProvider);
  return workspaceState.completedWorkspaces;
});

/// 기존 호환성을 위한 별칭 Provider
final workspaceProvider = unifiedWorkspaceProvider;
