import 'dart:async';
import 'dart:collection';
import 'dart:isolate';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'logger_utils.dart';

/// 비동기 처리 최적화 유틸리티
/// 
/// 병렬 처리, 배치 처리, 스레드 풀 관리 등을 통해 비동기 작업의 성능을 최적화합니다.
class AsyncOptimizer {
  static const String _tag = 'AsyncOptimizer';
  
  // 싱글톤 인스턴스
  static final AsyncOptimizer _instance = AsyncOptimizer._internal();
  factory AsyncOptimizer() => _instance;
  AsyncOptimizer._internal();

  // 작업 큐와 스레드 풀 (판매 현장 최적화)
  final List<Future<void> Function()> _taskQueue = [];
  final Map<String, Completer<dynamic>> _pendingTasks = {};
  int _maxConcurrentTasks = 12; // 대량 처리를 위한 증가
  int _currentRunningTasks = 0;

  /// 초기화 (판매 현장 최적화)
  void initialize({int? maxConcurrentTasks}) {
    // 판매 현장에서는 더 많은 동시 작업 허용
    _maxConcurrentTasks = maxConcurrentTasks ??
        (Platform.numberOfProcessors > 0 ? Platform.numberOfProcessors * 3 : 12);

    LoggerUtils.logInfo(
      'AsyncOptimizer initialized for sales environment with $_maxConcurrentTasks concurrent tasks',
      tag: _tag,
    );
  }

  /// 병렬 처리 실행
  static Future<List<T>> parallel<T>(
    List<Future<T> Function()> tasks, {
    int? maxConcurrency,
  }) async {
    final concurrency = maxConcurrency ?? 
        min(tasks.length, Platform.numberOfProcessors);
    
    LoggerUtils.logDebug(
      'Executing ${tasks.length} tasks with concurrency $concurrency',
      tag: _tag,
    );

    final results = <T>[];
    final semaphore = Semaphore(concurrency);
    
    final futures = tasks.map((task) async {
      await semaphore.acquire();
      try {
        return await task();
      } finally {
        semaphore.release();
      }
    }).toList();

    results.addAll(await Future.wait(futures));
    return results;
  }

  /// 배치 처리 실행 (판매 현장 최적화)
  static Future<List<T>> batch<T>(
    List<dynamic> items,
    Future<T> Function(dynamic item) processor, {
    int batchSize = 25, // 대량 처리를 위한 배치 크기 증가
    int? maxConcurrency,
  }) async {
    if (items.isEmpty) return [];

    final concurrency = maxConcurrency ?? Platform.numberOfProcessors;
    final results = <T>[];
    
    LoggerUtils.logDebug(
      'Processing ${items.length} items in batches of $batchSize with concurrency $concurrency',
      tag: _tag,
    );

    for (int i = 0; i < items.length; i += batchSize) {
      final batch = items.skip(i).take(batchSize).toList();
      
      final batchTasks = batch.map((item) => () => processor(item)).toList();
      final batchResults = await parallel(batchTasks, maxConcurrency: concurrency);
      
      results.addAll(batchResults);
    }

    return results;
  }

  /// 중복 작업 방지 (메모이제이션)
  static Future<T> memoize<T>(
    String key,
    Future<T> Function() task, {
    Duration? cacheDuration,
  }) async {
    final instance = AsyncOptimizer();
    
    // 이미 실행 중인 작업이 있으면 대기
    if (instance._pendingTasks.containsKey(key)) {
      return await instance._pendingTasks[key]!.future as T;
    }

    // 새로운 작업 시작
    final completer = Completer<T>();
    instance._pendingTasks[key] = completer;

    try {
      final result = await task();
      completer.complete(result);
      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      instance._pendingTasks.remove(key);
    }
  }

  /// 재시도 로직이 포함된 비동기 실행
  static Future<T> retry<T>(
    Future<T> Function() task, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 500),
    bool Function(dynamic error)? retryIf,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await task();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries || (retryIf != null && !retryIf(e))) {
          LoggerUtils.logError(
            'Task failed after $attempts attempts',
            tag: _tag,
            error: e,
          );
          rethrow;
        }
        
        LoggerUtils.logWarning(
          'Task failed (attempt $attempts/$maxRetries), retrying in ${delay.inMilliseconds}ms',
          tag: _tag,
        );
        
        await Future.delayed(delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * 1.5).round());
      }
    }
    
    throw Exception('Retry logic error'); // Should never reach here
  }

  /// 타임아웃이 포함된 비동기 실행
  static Future<T> timeout<T>(
    Future<T> Function() task,
    Duration timeoutDuration, {
    T? defaultValue,
  }) async {
    try {
      return await task().timeout(timeoutDuration);
    } on TimeoutException {
      LoggerUtils.logWarning(
        'Task timed out after ${timeoutDuration.inMilliseconds}ms',
        tag: _tag,
      );
      
      if (defaultValue != null) {
        return defaultValue;
      }
      rethrow;
    }
  }

  /// 스트림 배치 처리
  static Stream<List<T>> batchStream<T>(
    Stream<T> source, {
    int batchSize = 10,
    Duration? maxWaitTime,
  }) {
    final controller = StreamController<List<T>>();
    final buffer = <T>[];
    Timer? timer;

    void flushBuffer() {
      if (buffer.isNotEmpty) {
        controller.add(List.from(buffer));
        buffer.clear();
      }
      timer?.cancel();
      timer = null;
    }

    source.listen(
      (item) {
        buffer.add(item);
        
        if (buffer.length >= batchSize) {
          flushBuffer();
        } else if (maxWaitTime != null && timer == null) {
          timer = Timer(maxWaitTime, flushBuffer);
        }
      },
      onError: controller.addError,
      onDone: () {
        flushBuffer();
        controller.close();
      },
    );

    return controller.stream;
  }

  /// Isolate를 사용한 CPU 집약적 작업 처리
  static Future<T> compute<T, U>(
    T Function(U) callback,
    U message, {
    String? debugLabel,
  }) async {
    if (kIsWeb) {
      // 웹에서는 Isolate를 사용할 수 없으므로 메인 스레드에서 실행
      LoggerUtils.logWarning(
        'Running compute task on main thread (Web platform)',
        tag: _tag,
      );
      return callback(message);
    }

    LoggerUtils.logDebug(
      'Running compute task: ${debugLabel ?? 'unnamed'}',
      tag: _tag,
    );

    return await Isolate.run(() => callback(message));
  }

  /// 작업 큐에 작업 추가
  void addTask(Future<void> Function() task) {
    _taskQueue.add(task);
    _processQueue();
  }

  /// 작업 큐 처리
  void _processQueue() {
    if (_currentRunningTasks >= _maxConcurrentTasks || _taskQueue.isEmpty) {
      return;
    }

    final task = _taskQueue.removeAt(0);
    _currentRunningTasks++;

    task().whenComplete(() {
      _currentRunningTasks--;
      _processQueue(); // 다음 작업 처리
    });
  }

  /// 모든 작업 완료 대기
  Future<void> waitForAllTasks() async {
    while (_taskQueue.isNotEmpty || _currentRunningTasks > 0) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }

  /// 통계 정보 가져오기
  Map<String, dynamic> getStats() {
    return {
      'queuedTasks': _taskQueue.length,
      'runningTasks': _currentRunningTasks,
      'maxConcurrentTasks': _maxConcurrentTasks,
      'pendingTasks': _pendingTasks.length,
    };
  }
}

/// 세마포어 구현 (동시 실행 제한)
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
