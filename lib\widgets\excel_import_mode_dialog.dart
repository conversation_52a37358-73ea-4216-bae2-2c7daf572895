import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../screens/excel/excel_import_screen.dart'; // RegistrationMode enum 사용
import '../utils/dialog_theme.dart' as custom_dialog;
import '../utils/app_colors.dart';

// 엑셀 등록 모드/요일수집 다이얼로그 결과 데이터
class ExcelImportDialogResult {
  final RegistrationMode selectedMode;
  final bool collectDayOfWeek;
  final int dayOfWeekColumnIndex;
  ExcelImportDialogResult({
    required this.selectedMode,
    required this.collectDayOfWeek,
    required this.dayOfWeekColumnIndex,
  });
}

// 엑셀 등록 모드/요일수집 다이얼로그 위젯
class ExcelImportModeDialog extends StatefulWidget {
  final RegistrationMode? initialMode;
  final bool? initialCollectDayOfWeek;
  final int? initialDayOfWeekColumnIndex;
  const ExcelImportModeDialog({super.key, this.initialMode, this.initialCollectDayOfWeek, this.initialDayOfWeekColumnIndex});
  @override
  State<ExcelImportModeDialog> createState() => _ExcelImportModeDialogState();
}

class _ExcelImportModeDialogState extends State<ExcelImportModeDialog> {
  late RegistrationMode _selectedMode;
  late bool _collectDayOfWeek;
  late int _dayOfWeekColumnIndex;
  final TextEditingController _columnController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedMode = widget.initialMode ?? RegistrationMode.combined;
    _collectDayOfWeek = widget.initialCollectDayOfWeek ?? false;
    _dayOfWeekColumnIndex = widget.initialDayOfWeekColumnIndex ?? -1;
    if (_collectDayOfWeek && _dayOfWeekColumnIndex >= 0) {
      _columnController.text = _indexToColumnLetter(_dayOfWeekColumnIndex);
    }
  }

  @override
  void dispose() {
    _columnController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    // 모드 순서: 동시등록, 선입금만, 상품만
    final List<RegistrationMode> modeOrder = [
      RegistrationMode.combined,
      RegistrationMode.prepaymentOnly,
      RegistrationMode.productOnly,
    ];

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 아이콘과 제목을 한 줄로 배치
            Row(
              children: [
                custom_dialog.DialogTheme.buildCompactIconContainer(
                  icon: Icons.settings_rounded,
                  color: OnboardingColors.accent,
                  isTablet: isTablet,
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: Text(
                    '등록 모드 선택',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 내용
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 모드 선택 옵션들 (더 컴팩트)
                    ...modeOrder.map((mode) => _buildModeOption(mode, isTablet)),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                    // 요일 수집 설정 (더 컴팩트)
                    Container(
                      padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            OnboardingColors.surfaceVariant,
                            OnboardingColors.secondary.withValues(alpha: 0.3),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(isTablet ? 6.0 : 4.0),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      OnboardingColors.primary.withValues(alpha: 0.2),
                                      OnboardingColors.primaryLight.withValues(alpha: 0.1),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  Icons.calendar_today_rounded,
                                  color: OnboardingColors.primary,
                                  size: isTablet ? 16.0 : 14.0,
                                ),
                              ),
                              SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                              Expanded(
                                child: Text(
                                  '엑셀에서 요일 수집',
                                  style: TextStyle(
                                    fontSize: isTablet ? 14.0 : 13.0,
                                    fontWeight: FontWeight.w600,
                                    color: OnboardingColors.textPrimary,
                                  ),
                                ),
                              ),
                              Switch(
                                value: _collectDayOfWeek,
                                onChanged: (v) {
                                  setState(() { _collectDayOfWeek = v; });
                                },
                                activeColor: OnboardingColors.success,
                                activeTrackColor: OnboardingColors.success.withValues(alpha: 0.3),
                                inactiveThumbColor: OnboardingColors.secondary,
                                inactiveTrackColor: OnboardingColors.secondary.withValues(alpha: 0.3),
                              ),
                            ],
                          ),
                          if (_collectDayOfWeek) ...[
                            SizedBox(height: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                            Row(
                              children: [
                                Text(
                                  '요일 열:',
                                  style: TextStyle(
                                    fontSize: isTablet ? 13.0 : 12.0,
                                    color: OnboardingColors.textSecondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                                Container(
                                  width: isTablet ? 80.0 : 60.0,
                                  decoration: BoxDecoration(
                                    color: OnboardingColors.surface,
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: OnboardingColors.primary.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: TextField(
                                    controller: _columnController,
                                    decoration: InputDecoration(
                                      hintText: '예: E',
                                      hintStyle: TextStyle(
                                        color: OnboardingColors.textSecondary.withValues(alpha: 0.6),
                                        fontSize: isTablet ? 12.0 : 11.0,
                                      ),
                                      isDense: true,
                                      contentPadding: EdgeInsets.symmetric(
                                        vertical: isTablet ? 8.0 : 6.0,
                                        horizontal: isTablet ? 8.0 : 6.0,
                                      ),
                                      border: InputBorder.none,
                                    ),
                                    style: TextStyle(
                                      color: OnboardingColors.textPrimary,
                                      fontSize: isTablet ? 12.0 : 11.0,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(RegExp(r'^[A-Za-z]+')),
                                    ],
                                    onChanged: (v) {
                                      final upper = v.toUpperCase();
                                      if (_columnController.text != upper) {
                                        _columnController.value = TextEditingValue(
                                          text: upper,
                                          selection: TextSelection.collapsed(offset: upper.length),
                                        );
                                      }
                                      setState(() {
                                        _dayOfWeekColumnIndex = _columnLetterToIndex(upper);
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 0.5),
                                Text(
                                  '열',
                                  style: TextStyle(
                                    fontSize: isTablet ? 13.0 : 12.0,
                                    color: OnboardingColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 버튼들 (더 컴팩트)
            Row(
              children: [
                // 취소 버튼
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: OnboardingColors.secondary),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? 14.0 : 12.0,
                          ),
                          child: Text(
                            '취소',
                            style: TextStyle(
                              color: OnboardingColors.textSecondary,
                              fontSize: isTablet ? 16.0 : 14.0,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),

                // 확인 버튼
                Expanded(
                  child: custom_dialog.DialogTheme.buildGradientButton(
                    decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                    isCompact: true,
                    onPressed: () {
                      Navigator.of(context).pop(ExcelImportDialogResult(
                        selectedMode: _selectedMode,
                        collectDayOfWeek: _collectDayOfWeek,
                        dayOfWeekColumnIndex: _dayOfWeekColumnIndex,
                      ));
                    },
                    child: Text(
                      '확인',
                      style: TextStyle(
                        color: OnboardingColors.textOnPrimary,
                        fontSize: isTablet ? 16.0 : 14.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeOption(RegistrationMode mode, bool isTablet) {
    final isSelected = _selectedMode == mode;
    return Container(
      margin: EdgeInsets.symmetric(vertical: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 0.5),
      decoration: BoxDecoration(
        gradient: isSelected
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                OnboardingColors.primary.withValues(alpha: 0.15),
                OnboardingColors.primaryLight.withValues(alpha: 0.08),
              ],
            )
          : LinearGradient(
              colors: [
                OnboardingColors.surfaceVariant,
                OnboardingColors.secondary.withValues(alpha: 0.1),
              ],
            ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
            ? OnboardingColors.primary.withValues(alpha: 0.3)
            : OnboardingColors.secondary.withValues(alpha: 0.2),
          width: isSelected ? 2.0 : 1.0,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() { _selectedMode = mode; });
            HapticFeedback.lightImpact();
          },
          child: Padding(
            padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 8.0 : 6.0),
                  decoration: BoxDecoration(
                    gradient: isSelected
                      ? OnboardingColors.primaryGradient
                      : LinearGradient(
                          colors: [
                            OnboardingColors.secondary,
                            OnboardingColors.secondaryLight,
                          ],
                        ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    mode.icon,
                    color: isSelected
                      ? OnboardingColors.textOnPrimary
                      : OnboardingColors.textSecondary,
                    size: isTablet ? 20.0 : 16.0,
                  ),
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mode.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: isTablet ? 16.0 : 14.0,
                          color: isSelected
                            ? OnboardingColors.primary
                            : OnboardingColors.textPrimary,
                          fontFamily: 'Pretendard',
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        mode.description,
                        style: TextStyle(
                          fontSize: isTablet ? 13.0 : 11.0,
                          color: OnboardingColors.textSecondary,
                          height: 1.2,
                          fontFamily: 'Pretendard',
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.all(isTablet ? 6.0 : 4.0),
                    decoration: BoxDecoration(
                      gradient: OnboardingColors.primaryGradient,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.check_rounded,
                      color: OnboardingColors.textOnPrimary,
                      size: isTablet ? 16.0 : 14.0,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int _columnLetterToIndex(String columnLetter) {
    if (columnLetter.isEmpty) return -1;
    columnLetter = columnLetter.toUpperCase();
    int result = 0;
    for (int i = 0; i < columnLetter.length; i++) {
      result = result * 26 + (columnLetter.codeUnitAt(i) - 64);
    }
    return result - 1;
  }

  String _indexToColumnLetter(int index) {
    if (index < 0) return '';
    String result = '';
    int currentIndex = index;
    while (currentIndex >= 0) {
      result = String.fromCharCode((currentIndex % 26) + 65) + result;
      currentIndex = (currentIndex ~/ 26) - 1;
    }
    return result;
  }
} 