import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';

/// 최적화된 Provider 기본 클래스
/// 
/// 모든 Provider에서 공통으로 사용할 수 있는 최적화 기능을 제공합니다.
/// - 메모리 관리
/// - 성능 모니터링
/// - 에러 처리
/// - 로깅
abstract class OptimizedProviderBase<T> extends StateNotifier<T> {
  final String _tag;
  final bool _enableLogging;
  final bool _enablePerformanceMonitoring;

  OptimizedProviderBase(
    T initialState, {
    String? tag,
    bool enableLogging = true,
    bool enablePerformanceMonitoring = true,
  })  : _tag = tag ?? 'OptimizedProvider',
        _enableLogging = enableLogging,
        _enablePerformanceMonitoring = enablePerformanceMonitoring,
        super(initialState);

  /// 상태 업데이트 (로깅 포함)
  void updateState(T newState) {
    if (_enableLogging) {
      LoggerUtils.logDebug(
        '상태 업데이트: ${state.runtimeType} -> ${newState.runtimeType}',
        tag: _tag,
      );
    }
    state = newState;
  }

  /// 에러 처리 (로깅 포함)
  void handleError(dynamic error, [StackTrace? stackTrace]) {
    LoggerUtils.logError(
      'Provider 에러 발생',
      error: error,
      stackTrace: stackTrace,
      tag: _tag,
    );
  }

  /// 성능 측정
  Future<R> measurePerformance<R>(
    Future<R> Function() operation, {
    String? operationName,
  }) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      
      if (_enablePerformanceMonitoring) {
        LoggerUtils.logInfo(
          '성능 측정: ${operationName ?? 'operation'} - ${stopwatch.elapsedMilliseconds}ms',
          tag: _tag,
        );
      }
      
      return result;
    } catch (e, stackTrace) {
      stopwatch.stop();
      LoggerUtils.logError(
        '성능 측정 중 에러: ${operationName ?? 'operation'} - ${stopwatch.elapsedMilliseconds}ms',
        error: e,
        stackTrace: stackTrace,
        tag: _tag,
      );
      rethrow;
    }
  }

  /// 메모리 사용량 로깅
  void logMemoryUsage() {
    if (_enableLogging) {
      // 메모리 사용량 정보를 로깅할 수 있습니다
      LoggerUtils.logDebug(
        '메모리 사용량 로깅',
        tag: _tag,
      );
    }
  }

  /// Provider 정리
  @override
  void dispose() {
    if (_enableLogging) {
      LoggerUtils.logInfo('Provider 정리: $_tag', tag: _tag);
    }
    super.dispose();
  }
} 