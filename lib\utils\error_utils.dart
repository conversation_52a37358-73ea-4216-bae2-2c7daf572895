import 'package:flutter/material.dart';
import 'logger_utils.dart';
import 'provider_exception.dart';

/// 오류 타입 정의
enum ErrorType { network, database, validation, authentication, unknown }

/// 앱 전역 에러 처리 및 사용자 메시지 변환을 지원하는 유틸리티 클래스입니다.
/// - 에러 타입(enum), 메시지 변환, SnackBar/다이얼로그 등 UI 연동 지원
/// - riverpod 3.x Provider와 연동, 네트워크/DB/유효성/알 수 없는 에러 등 구분
class ErrorUtils {
  /// 오류 메시지 생성
  static String getErrorMessage(ErrorType type, String message) {
    switch (type) {
      case ErrorType.network:
        return '네트워크 오류: $message';
      case ErrorType.database:
        return '데이터베이스 오류: $message';
      case ErrorType.validation:
        return '유효성 검사 오류: $message';
      case ErrorType.authentication:
        return '인증 오류: $message';
      case ErrorType.unknown:
        return '알 수 없는 오류: $message';
    }
  }

  /// 오류 처리 및 로깅
  static void handleError(
    BuildContext context,
    Object error, {
    ErrorType type = ErrorType.unknown,
    String? customMessage,
    StackTrace? stackTrace,
    String tag = 'ErrorHandler',
  }) {
    // 오류 로깅
    LoggerUtils.error(
      customMessage ?? error.toString(),
      tag: tag,
      error: error,
      stackTrace: stackTrace,
    );

    // 사용자에게 오류 메시지 표시
    final message = customMessage ?? getErrorMessage(type, error.toString());
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: '확인',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// 오류 다이얼로그 표시
  static Future<void> showErrorDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  /// 오류 처리 래퍼 함수
  static Future<T?> wrapError<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String? errorMessage,
    ErrorType type = ErrorType.unknown,
    String tag = 'ErrorWrapper',
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      handleError(
        context,
        e,
        type: type,
        customMessage: errorMessage,
        stackTrace: stackTrace,
        tag: tag,
      );
      return null;
    }
  }

  /// 오류 처리 및 로깅 (void 버전)
  static Future<void> wrapErrorVoid(
    BuildContext context,
    Future<void> Function() action, {
    required String errorMessage,
    required ErrorType type,
    required String tag,
  }) async {
    try {
      await action();
    } catch (e, stackTrace) {
      LoggerUtils.error(
        errorMessage,
        tag: tag,
        error: e,
        stackTrace: stackTrace,
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(getErrorMessage(type, errorMessage)),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
          ),
        );
      }

      rethrow;
    }
  }

  /// 에러를 AppError로 변환 (provider_exception.dart에서 이동)
  static AppError fromException(dynamic error, [StackTrace? stackTrace]) {
    if (error is AppError) return error;
    
    final errorString = error.toString().toLowerCase();
    
    // 데이터베이스 에러 감지
    if (errorString.contains('database') || 
        errorString.contains('sql') || 
        errorString.contains('sqflite')) {
      return DatabaseError(
        message: error.toString(),
        originalError: error,
      );
    }
    
    // 네트워크 에러 감지
    if (errorString.contains('network') || 
        errorString.contains('connection') || 
        errorString.contains('timeout')) {
      return NetworkError(
        message: error.toString(),
      );
    }
    
    // 파일 에러 감지
    if (errorString.contains('file') || 
        errorString.contains('path') || 
        errorString.contains('permission')) {
      return FileError(
        operation: 'unknown',
        filePath: 'unknown',
        message: error.toString(),
        type: FileErrorType.accessDenied,
      );
    }
    
    // 알 수 없는 에러
    return UnknownError(
      message: error.toString(),
      originalError: error,
      stackTrace: stackTrace,
    );
  }
}
