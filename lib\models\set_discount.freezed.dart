// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_discount.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SetDiscount {

 int? get id; String get name; int get discountAmount; List<int> get productIds; bool get isActive; DateTime get createdAt; DateTime? get updatedAt; int get eventId;
/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SetDiscountCopyWith<SetDiscount> get copyWith => _$SetDiscountCopyWithImpl<SetDiscount>(this as SetDiscount, _$identity);

  /// Serializes this SetDiscount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SetDiscount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&const DeepCollectionEquality().equals(other.productIds, productIds)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,discountAmount,const DeepCollectionEquality().hash(productIds),isActive,createdAt,updatedAt,eventId);

@override
String toString() {
  return 'SetDiscount(id: $id, name: $name, discountAmount: $discountAmount, productIds: $productIds, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $SetDiscountCopyWith<$Res>  {
  factory $SetDiscountCopyWith(SetDiscount value, $Res Function(SetDiscount) _then) = _$SetDiscountCopyWithImpl;
@useResult
$Res call({
 int? id, String name, int discountAmount, List<int> productIds, bool isActive, DateTime createdAt, DateTime? updatedAt, int eventId
});




}
/// @nodoc
class _$SetDiscountCopyWithImpl<$Res>
    implements $SetDiscountCopyWith<$Res> {
  _$SetDiscountCopyWithImpl(this._self, this._then);

  final SetDiscount _self;
  final $Res Function(SetDiscount) _then;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = null,Object? discountAmount = null,Object? productIds = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? eventId = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,productIds: null == productIds ? _self.productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [SetDiscount].
extension SetDiscountPatterns on SetDiscount {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SetDiscount value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SetDiscount value)  $default,){
final _that = this;
switch (_that) {
case _SetDiscount():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SetDiscount value)?  $default,){
final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String name,  int discountAmount,  List<int> productIds,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that.id,_that.name,_that.discountAmount,_that.productIds,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String name,  int discountAmount,  List<int> productIds,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)  $default,) {final _that = this;
switch (_that) {
case _SetDiscount():
return $default(_that.id,_that.name,_that.discountAmount,_that.productIds,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String name,  int discountAmount,  List<int> productIds,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)?  $default,) {final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that.id,_that.name,_that.discountAmount,_that.productIds,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SetDiscount implements SetDiscount {
  const _SetDiscount({this.id, required this.name, required this.discountAmount, required final  List<int> productIds, this.isActive = true, required this.createdAt, this.updatedAt, this.eventId = 1}): _productIds = productIds;
  factory _SetDiscount.fromJson(Map<String, dynamic> json) => _$SetDiscountFromJson(json);

@override final  int? id;
@override final  String name;
@override final  int discountAmount;
 final  List<int> _productIds;
@override List<int> get productIds {
  if (_productIds is EqualUnmodifiableListView) return _productIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_productIds);
}

@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  int eventId;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetDiscountCopyWith<_SetDiscount> get copyWith => __$SetDiscountCopyWithImpl<_SetDiscount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SetDiscountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetDiscount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&const DeepCollectionEquality().equals(other._productIds, _productIds)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,discountAmount,const DeepCollectionEquality().hash(_productIds),isActive,createdAt,updatedAt,eventId);

@override
String toString() {
  return 'SetDiscount(id: $id, name: $name, discountAmount: $discountAmount, productIds: $productIds, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class _$SetDiscountCopyWith<$Res> implements $SetDiscountCopyWith<$Res> {
  factory _$SetDiscountCopyWith(_SetDiscount value, $Res Function(_SetDiscount) _then) = __$SetDiscountCopyWithImpl;
@override @useResult
$Res call({
 int? id, String name, int discountAmount, List<int> productIds, bool isActive, DateTime createdAt, DateTime? updatedAt, int eventId
});




}
/// @nodoc
class __$SetDiscountCopyWithImpl<$Res>
    implements _$SetDiscountCopyWith<$Res> {
  __$SetDiscountCopyWithImpl(this._self, this._then);

  final _SetDiscount _self;
  final $Res Function(_SetDiscount) _then;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = null,Object? discountAmount = null,Object? productIds = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? eventId = null,}) {
  return _then(_SetDiscount(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,productIds: null == productIds ? _self._productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
