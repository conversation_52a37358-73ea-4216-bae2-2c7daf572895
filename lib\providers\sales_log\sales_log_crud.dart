import 'dart:developer' as developer;
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../repositories/sales_log_repository.dart';
import '../../repositories/product_repository.dart';
import '../../utils/logger_utils.dart';
import '../../utils/provider_exception.dart';
import 'sales_log_state.dart';

/// 판매 기록 CRUD 작업을 담당하는 클래스
///
/// 주요 기능:
/// - 판매 기록 추가/수정/삭제
/// - 재고 복구 로직
/// - 묶음 판매 처리
/// - 오류 처리 및 로깅
class SalesLogCrud {
  static const String _tag = 'SalesLogCrud';
  static const String _domain = 'SLG_CRUD';

  final SalesLogRepository _salesLogRepository;
  final ProductRepository _productRepository;
  final dynamic Function(SalesLogState) _updateState;
  final Future<void> Function() _refreshState;

  SalesLogCrud({
    required SalesLogRepository salesLogRepository,
    required ProductRepository productRepository,
    required dynamic Function(SalesLogState) updateState,
    required Future<void> Function() refreshState,
  })  : _salesLogRepository = salesLogRepository,
        _productRepository = productRepository,
        _updateState = updateState,
        _refreshState = refreshState;

  /// 판매 기록 추가
  Future<SalesLog> addSalesLog(SalesLog newSalesLog) async {
    LoggerUtils.logInfo('판매 기록 저장 시작...', tag: 'SalesLogCrud');

    try {
      final savedSalesLog = await _salesLogRepository.addSalesLog(newSalesLog);
      LoggerUtils.logInfo('판매 기록 저장 완료: ${savedSalesLog.toMap()}', tag: 'SalesLogCrud');
      
      // SalesLogNotifier에서 효율적인 상태 갱신을 하므로 여기서는 _refreshState() 호출하지 않음
      LoggerUtils.logInfo('상태 갱신은 SalesLogNotifier에서 처리됨', tag: 'SalesLogCrud');
      
      return savedSalesLog;
    } catch (e, stackTrace) {
      LoggerUtils.logError('❌ 판매 기록 추가 실패: $e', tag: 'SalesLogCrud', error: e, stackTrace: stackTrace);
      throw ProviderException(
        message: '판매 기록을 추가하는 중 오류가 발생했습니다',
        code: '${_domain}_ADD_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 판매 기록 수정
  Future<void> updateSalesLog(SalesLog salesLog) async {
    LoggerUtils.methodStart('updateSalesLog', tag: _tag);

    try {
      await _salesLogRepository.updateSalesLog(salesLog);
      await _refreshState();
      
      LoggerUtils.methodEnd('updateSalesLog', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매 기록 수정 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매 기록을 수정하는 중 오류가 발생했습니다',
        code: '${_domain}_UPDATE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 판매 기록 삭제
  Future<void> deleteSalesLog(int id) async {
    LoggerUtils.methodStart('deleteSalesLog', tag: _tag);

    try {
      await _salesLogRepository.deleteSalesLog(id);
      await _refreshState();
      
      LoggerUtils.methodEnd('deleteSalesLog', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매 기록 삭제 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매 기록을 삭제하는 중 오류가 발생했습니다',
        code: '${_domain}_DELETE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    }
  }

  /// 단일 판매 기록 삭제 및 재고 복구
  Future<String> deleteSingleSaleAndUpdateStock(SalesLog salesLog) async {
    LoggerUtils.methodStart('deleteSingleSaleAndUpdateStock', tag: _tag);

    try {
      bool success = true;
      String message;

      developer.log(
        '판매기록 삭제 시작: ${salesLog.productName}, ID: ${salesLog.productId}, 수량: ${salesLog.soldQuantity}, 타입: ${salesLog.transactionType}',
        name: 'SalesLogCrud',
      );

      if (salesLog.transactionType == TransactionType.sale &&
          salesLog.productId != null) {
        developer.log('재고 복구 시도 중...', name: 'SalesLogCrud');
        try {
          // 직접 increaseStock 메서드 사용으로 중복 Firebase 업로드 방지
          await _productRepository.increaseStock(salesLog.productId!, salesLog.soldQuantity);
          developer.log('재고 복구 완료', name: 'SalesLogCrud');
        } catch (e) {
          developer.log(
            '상품을 찾을 수 없음 또는 재고 복구 실패 (ID: ${salesLog.productId}): $e',
            name: 'SalesLogCrud',
          );
          success = false;
        }
      } else {
        developer.log(
          '재고 복구 대상이 아님 (타입: ${salesLog.transactionType}, 상품ID: ${salesLog.productId})',
          name: 'SalesLogCrud',
        );
      }

      developer.log('판매기록 삭제 중...', name: 'SalesLogCrud');
      await _salesLogRepository.deleteSalesLog(salesLog.id);
      developer.log('판매기록 삭제 완료', name: 'SalesLogCrud');

      if (success) {
        if (salesLog.transactionType == TransactionType.sale &&
            salesLog.productId != null) {
          message = "'${salesLog.productName}' 판매 기록 삭제 및 재고 복구 완료.";
        } else {
          message = "'${salesLog.productName}' 기록 삭제 완료 (재고 변경 없음).";
        }
      } else {
        message =
            "'${salesLog.productName}' 판매 기록 삭제 완료.\n\n⚠️ 재고 복구 실패: 해당 상품이 이미 삭제되어 재고를 복원할 수 없습니다.";
      }

      developer.log('판매기록 데이터 새로고침 중...', name: 'SalesLogCrud');
      await _refreshState();
      developer.log('판매기록 데이터 새로고침 완료', name: 'SalesLogCrud');
      
      LoggerUtils.methodEnd('deleteSingleSaleAndUpdateStock', tag: _tag);
      return message;
    } catch (e, stackTrace) {
      developer.log('오류 발생: $e', name: 'SalesLogCrud');
      LoggerUtils.logError(
        'Error in deleteSingleSaleAndUpdateStock',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      final errorMessage = "기록 삭제 중 오류 발생: ${e.toString()}";

      _updateState(SalesLogState(errorMessage: errorMessage));
      return errorMessage;
    }
  }

  /// 묶음 판매 기록 삭제 및 재고 복구
  Future<String> deleteGroupedSaleAndUpdateStock(
    GroupedSale groupedSale,
  ) async {
    LoggerUtils.methodStart('deleteGroupedSaleAndUpdateStock', tag: _tag);

    try {
      bool allItemsSuccess = true;
      final batchIdToDelete = groupedSale.batchId;
      int totalRestoredQuantity = 0;
      final representativeProductName = groupedSale.items.isNotEmpty
          ? groupedSale.items.first.productName
          : "알 수 없는 상품";

      for (final salesLog in groupedSale.items) {
        if (salesLog.productId != null &&
            (salesLog.transactionType == TransactionType.sale ||
                salesLog.transactionType == TransactionType.serviceGift)) {
          try {
            // 직접 increaseStock 메서드 사용으로 중복 Firebase 업로드 방지
            await _productRepository.increaseStock(salesLog.productId!, salesLog.soldQuantity);
            totalRestoredQuantity += salesLog.soldQuantity;
          } catch (e) {
            allItemsSuccess = false;
          }
        }
      }

      for (final salesLog in groupedSale.items) {
        await _salesLogRepository.deleteSalesLog(salesLog.id);
      }

      final message = allItemsSuccess
          ? (totalRestoredQuantity > 0
                ? "묶음 판매 '$representativeProductName' (ID: $batchIdToDelete) 기록 삭제 및 관련 상품 재고 $totalRestoredQuantity개 복구 완료."
                : "묶음 판매 '$representativeProductName' (ID: $batchIdToDelete) 기록 삭제 완료 (재고 변경 없음).")
          : "묶음 판매 '$representativeProductName' (ID: $batchIdToDelete) 기록 삭제 완료.\n\n⚠️ 일부 상품이 이미 삭제되어 재고를 복원할 수 없습니다.";

      await _refreshState();
      
      LoggerUtils.methodEnd('deleteGroupedSaleAndUpdateStock', tag: _tag);
      return message;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in deleteGroupedSaleAndUpdateStock',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      final errorMessage =
          "묶음 판매 기록 삭제 중 오류 발생 (ID: ${groupedSale.batchId}): ${e.toString()}";

      _updateState(SalesLogState(errorMessage: errorMessage));
      return errorMessage;
    }
  }

  /// 판매 기록 ID로 조회
  Future<SalesLog?> getSalesLogById(int id) async {
    LoggerUtils.methodStart('getSalesLogById', tag: _tag);

    try {
      final salesLog = await _salesLogRepository.getLogById(id);
      
      LoggerUtils.methodEnd('getSalesLogById', tag: _tag);
      return salesLog;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'Error in getSalesLogById',
        tag: _tag,
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      );

      _updateState(SalesLogState(
        errorMessage: '판매 기록을 조회하는 중 오류가 발생했습니다: $e',
      ));
      return null;
    }
  }

  /// 판매자별 판매 기록 로드
  Future<void> loadSalesLogsBySeller(String sellerName) async {
    LoggerUtils.methodStart('loadSalesLogsBySeller', tag: _tag);

    try {
      _updateState(SalesLogState(
        isUpdating: true,
        selectedSellerFilter: sellerName,
      ));

      final salesLogs = await _salesLogRepository.getSalesLogsBySeller(sellerName);

      final filteredSalesLogs = List<SalesLog>.from(salesLogs);
      _updateState(SalesLogState(
        salesLogs: salesLogs,
        filteredSalesLogs: filteredSalesLogs,
        displayItems: _createDisplayItems(filteredSalesLogs),
        isUpdating: false,
      ));
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매자별 판매 기록 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매자별 판매 기록을 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_BY_SELLER_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsByType(TransactionType transactionType) async {
    LoggerUtils.methodStart('loadSalesLogsByType', tag: _tag);

    try {
      _updateState(SalesLogState(
        isUpdating: true,
        selectedTypeFilter: transactionType,
      ));

      final salesLogs = await _salesLogRepository.getAllSalesLogsByType(transactionType);

      final filteredSalesLogs = List<SalesLog>.from(salesLogs);
      _updateState(SalesLogState(
        salesLogs: salesLogs,
        filteredSalesLogs: filteredSalesLogs,
        displayItems: _createDisplayItems(filteredSalesLogs),
        isUpdating: false,
      ));
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '거래 타입별 판매 기록 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '거래 타입별 판매 기록을 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_BY_TYPE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 판매자 및 거래 타입별 판매 기록 로드
  Future<void> loadSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    LoggerUtils.methodStart('loadSalesLogsBySellerAndType', tag: _tag);

    try {
      _updateState(SalesLogState(
        isUpdating: true,
        selectedSellerFilter: sellerName,
        selectedTypeFilter: transactionType,
      ));

      final salesLogs = await _salesLogRepository.getSalesLogsBySellerAndType(
        sellerName,
        transactionType,
      );

      final filteredSalesLogs = List<SalesLog>.from(salesLogs);
      _updateState(SalesLogState(
        salesLogs: salesLogs,
        filteredSalesLogs: filteredSalesLogs,
        displayItems: _createDisplayItems(filteredSalesLogs),
        isUpdating: false,
      ));
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '판매자 및 거래 타입별 판매 기록 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '판매자 및 거래 타입별 판매 기록을 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_BY_SELLER_TYPE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 날짜 범위별 판매 기록 로드
  Future<void> loadSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    LoggerUtils.methodStart('loadSalesLogsByDateRange', tag: _tag);

    try {
      _updateState(SalesLogState(isUpdating: true));

      final salesLogs = await _salesLogRepository.getSalesLogsByDateRange(
        startDate,
        endDate,
      );

      final filteredSalesLogs = List<SalesLog>.from(salesLogs);
      _updateState(SalesLogState(
        salesLogs: salesLogs,
        filteredSalesLogs: filteredSalesLogs,
        displayItems: _createDisplayItems(filteredSalesLogs),
        isUpdating: false,
      ));
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '날짜 범위별 판매 기록 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '날짜 범위별 판매 기록을 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_BY_DATE_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 배치 ID별 판매 기록 로드
  Future<void> loadSalesLogsByBatchId(String batchId) async {
    LoggerUtils.methodStart('loadSalesLogsByBatchId', tag: _tag);

    try {
      _updateState(SalesLogState(isUpdating: true));

      final salesLogs = await _salesLogRepository.getSalesLogsByBatchId(batchId);

      final filteredSalesLogs = List<SalesLog>.from(salesLogs);
      _updateState(SalesLogState(
        salesLogs: salesLogs,
        filteredSalesLogs: filteredSalesLogs,
        displayItems: _createDisplayItems(filteredSalesLogs),
        isUpdating: false,
      ));
    } catch (e, stackTrace) {
      LoggerUtils.error(
        '배치 ID별 판매 기록 로드 실패',
        error: e is Exception ? e : Exception(e.toString()),
        tag: _tag,
        stackTrace: stackTrace,
      );
      throw ProviderException(
        message: '배치 ID별 판매 기록을 로드하는 중 오류가 발생했습니다',
        code: '${_domain}_LOAD_BY_BATCH_ERROR',
        error: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
        severity: ProviderExceptionSeverity.error,
      );
    } finally {
      _updateState(SalesLogState(isUpdating: false));
    }
  }

  /// 표시 아이템 생성 (기존 로직과 동일)
  List<SalesLogDisplayItem> _createDisplayItems(List<SalesLog> logs) {
    final Map<String?, List<SalesLog>> groupedLogs = {};

    for (final log in logs) {
      final batchId = log.batchSaleId;
      if (batchId != null) {
        groupedLogs.putIfAbsent(batchId, () => []).add(log);
      }
    }

    final List<SalesLogDisplayItem> displayItems = [];

    for (final entry in groupedLogs.entries) {
      displayItems.add(GroupedSale(entry.value, entry.key!));
    }

    for (final log in logs.where((log) => log.batchSaleId == null)) {
      displayItems.add(SingleItem(log));
    }

    displayItems.sort(
      (a, b) => b.representativeTimestampMillis().compareTo(
        a.representativeTimestampMillis(),
      ),
    );

    return displayItems;
  }
} 