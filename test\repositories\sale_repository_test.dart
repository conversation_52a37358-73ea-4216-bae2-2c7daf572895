import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/models/sale.dart';
import 'package:parabara/repositories/sale_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;
  late SaleRepository repository;

  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE sales (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              productId INTEGER NOT NULL,
              quantity INTEGER NOT NULL,
              totalPrice INTEGER NOT NULL,
              saleTimestamp INTEGER NOT NULL,
              name TEXT,
              sellerName TEXT,
              imagePath TEXT
            )
          ''');
        },
      ),
    );

    databaseService = TestDatabaseService(db);
    repository = SaleRepository(database: databaseService);

    // 테스트 데이터 삽입
    await db.insert('sales', {
      'productId': 1,
      'quantity': 1,
      'totalPrice': 1000,
      'saleTimestamp': DateTime.now().millisecondsSinceEpoch,
      'name': 'Test Sale',
      'sellerName': 'Test Seller',
    });
  });

  tearDown(() async {
    await db.close();
  });

  group('SaleRepository SQL Injection Tests', () {
    test('getAllSales should safely handle table queries', () async {
      final sales = await repository.getAllSales();

      // 결과가 정상적인 Sale 객체들만 포함해야 함
      for (final sale in sales) {
        expect(sale, isA<Sale>());
        expect(sale.name, isNotNull);
      }
    });

    test('getSaleById should safely handle malicious IDs', () async {
      // 악의적인 ID 값들
      final maliciousIds = [
        -1,
        999999,
        0,
      ];

      for (final id in maliciousIds) {
        final sale = await repository.getSaleById(id);
        // 존재하지 않는 ID에 대해서는 null이 반환되어야 함
        expect(sale, isNull);
      }
    });

    test('insertSale should safely handle malicious data', () async {
      final maliciousSale = Sale(
        id: 2, // ID를 2로 변경하여 중복 방지
        productId: 1,
        quantity: 1,
        totalPrice: 1000,
        saleTimestamp: DateTime.now(),
        name: "'; DROP TABLE sales; --",
        sellerName: "' OR '1'='1",
        imagePath: "'; DELETE FROM sales; --",
      );

      // 악의적인 데이터가 정상적으로 처리되어야 함
      await repository.insertSale(maliciousSale);

      // 삽입된 데이터가 정상적으로 조회되어야 함
      final retrievedSale = await repository.getSaleById(2);
      expect(retrievedSale, isNotNull);
      expect(retrievedSale!.name, equals("'; DROP TABLE sales; --"));
    });

    test('updateSale should safely handle malicious data', () async {
      // 먼저 정상적인 데이터 삽입
      final originalSale = Sale(
        id: 3, // ID를 3으로 변경하여 중복 방지
        productId: 2,
        quantity: 1,
        totalPrice: 2000,
        saleTimestamp: DateTime.now(),
        name: 'Original Sale',
        sellerName: 'Original Seller',
      );
      await repository.insertSale(originalSale);

      // 악의적인 데이터로 업데이트
      final maliciousSale = originalSale.copyWith(
        name: "'; DROP TABLE sales; --",
        sellerName: "' OR '1'='1",
      );

      await repository.updateSale(maliciousSale);

      // 업데이트된 데이터가 정상적으로 조회되어야 함
      final retrievedSale = await repository.getSaleById(3);
      expect(retrievedSale, isNotNull);
      expect(retrievedSale!.name, equals("'; DROP TABLE sales; --"));
    });

    test('deleteSale should safely handle malicious IDs', () async {
      // 정상적인 데이터 삽입
      final sale = Sale(
        id: 4, // ID를 4로 변경하여 중복 방지
        productId: 3,
        quantity: 1,
        totalPrice: 3000,
        saleTimestamp: DateTime.now(),
        name: 'Test Sale for Delete',
        sellerName: 'Test Seller',
      );
      await repository.insertSale(sale);

      // 악의적인 ID로 삭제 시도
      final maliciousSale = sale.copyWith(id: 999999);
      await repository.deleteSale(maliciousSale);

      // 원본 데이터는 그대로 남아있어야 함
      final retrievedSale = await repository.getSaleById(4);
      expect(retrievedSale, isNotNull);
    });
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;

  @override
  Future<void> forceMigration() async {}
}
