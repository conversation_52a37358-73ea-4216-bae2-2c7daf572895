// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'set_discount.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SetDiscount _$SetDiscountFromJson(Map<String, dynamic> json) => _SetDiscount(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  discountAmount: (json['discountAmount'] as num).toInt(),
  productIds: (json['productIds'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
  isActive: json['isActive'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  eventId: (json['eventId'] as num?)?.toInt() ?? 1,
);

Map<String, dynamic> _$SetDiscountToJson(_SetDiscount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'discountAmount': instance.discountAmount,
      'productIds': instance.productIds,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'eventId': instance.eventId,
    };
