import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'dimens.dart';

// ============================================================================
// 통합 애니메이션 시스템
// 
// 이 파일은 다음 4개 파일을 통합한 것입니다:
// - animation_constants.dart -> AnimationConstants 클래스
// - animation_widgets.dart -> AnimationWidgets 클래스  
// - loading_animations.dart -> LoadingAnimations 클래스
// - animation_utils.dart -> AnimationUtils 클래스
//
// 모든 기존 API는 100% 호환성을 유지합니다.
// ============================================================================

/// 애니메이션 상수들을 정의하는 클래스
class AnimationConstants {
  AnimationConstants._();

  // ============ Duration Constants ============
  /// 2025 트렌드: 빠르고 반응형 애니메이션
  static const Duration ultraFast = Duration(milliseconds: 100);
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration extraSlow = Duration(milliseconds: 800);

  // ============ Curve Constants ============
  /// Material Design 3 Expressive 커브
  static const Curve expressiveEase = Curves.easeInOutCubic;
  static const Curve expressiveEaseIn = Curves.easeInCubic;
  static const Curve expressiveEaseOut = Curves.easeOutCubic;

  // ============ Scale Values ============
  static const double microScale = 0.98;
  static const double smallScale = 0.95;
  static const double mediumScale = 0.9;

  // ============ Page Transition Constants ============
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);
  static const Curve pageTransitionCurve = Curves.easeInOutCubic;

  // ============ Loading Animation Constants ============
  static const Duration pulseDuration = Duration(milliseconds: 1500);
  static const Duration spinDuration = Duration(milliseconds: 1500);
  static const double defaultPulseSize = 50.0;
  static const double defaultSpinSize = 24.0;
  static const double defaultStrokeWidth = 2.0;

  // ============ Shimmer Constants ============
  static const Duration shimmerDuration = Duration(milliseconds: 1500);

  // ============ Card Animation Constants ============
  static const double defaultHoverScale = 1.02;
  static const double defaultElevation = 4.0;
  static const double defaultHoverElevation = 8.0;

  // ============ List Animation Constants ============
  static const Duration listItemDelay = Duration(milliseconds: 50);
}

/// 공통 애니메이션 위젯들을 제공하는 클래스
class AnimationWidgets {
  AnimationWidgets._();

  /// 버튼 탭 애니메이션 (Material 3 Expressive)
  static Widget createTapAnimation({
    required Widget child,
    required VoidCallback onTap,
    double scale = AnimationConstants.microScale,
    Duration duration = AnimationConstants.fast,
    bool enableHaptics = true,
  }) {
    return _TapAnimationWidget(
      onTap: onTap,
      scale: scale,
      duration: duration,
      enableHaptics: enableHaptics,
      child: child,
    );
  }

  /// Fade In 애니메이션
  static Widget createFadeIn({
    required Widget child,
    Duration duration = AnimationConstants.medium,
    Curve curve = AnimationConstants.expressiveEase,
    double initialOpacity = 0.0,
    Offset? initialOffset,
  }) {
    return _FadeInWidget(
      duration: duration,
      curve: curve,
      initialOpacity: initialOpacity,
      initialOffset: initialOffset,
      child: child,
    );
  }

  /// 리스트 아이템 등장 애니메이션
  static Widget createListItemAnimation({
    required Widget child,
    required int index,
    Duration duration = AnimationConstants.medium,
    Duration delay = AnimationConstants.listItemDelay,
  }) {
    return _ListItemAnimationWidget(
      index: index,
      duration: duration,
      delay: delay,
      child: child,
    );
  }

  /// 카드 호버 애니메이션
  static Widget createCardHover({
    required Widget child,
    double hoverScale = AnimationConstants.defaultHoverScale,
    Duration duration = AnimationConstants.fast,
    double elevation = AnimationConstants.defaultElevation,
    double hoverElevation = AnimationConstants.defaultHoverElevation,
  }) {
    return _CardHoverWidget(
      hoverScale: hoverScale,
      duration: duration,
      elevation: elevation,
      hoverElevation: hoverElevation,
      child: child,
    );
  }

  /// Shimmer 로딩 애니메이션
  static Widget createShimmer({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration duration = AnimationConstants.shimmerDuration,
  }) {
    return _ShimmerWidget(
      baseColor: baseColor,
      highlightColor: highlightColor,
      duration: duration,
      child: child,
    );
  }
}

/// 로딩 애니메이션 위젯들을 제공하는 클래스
class LoadingAnimations {
  LoadingAnimations._();

  /// 펄스 로딩 애니메이션
  static Widget createPulseLoading({double size = AnimationConstants.defaultPulseSize, Color? color}) {
    return _PulseLoadingWidget(size: size, color: color);
  }

  /// 회전 로딩 애니메이션
  static Widget createSpinningLoading({
    double size = AnimationConstants.defaultSpinSize,
    Color? color,
    double strokeWidth = AnimationConstants.defaultStrokeWidth,
  }) {
    return _SpinningLoadingWidget(
      size: size,
      color: color,
      strokeWidth: strokeWidth,
    );
  }
}

/// 애니메이션 효과/전환/지연 등을 지원하는 유틸리티 클래스입니다.
/// - riverpod 3.x Provider/Repository와 연동, UI/UX 개선 목적
///
/// 이 클래스는 앱 전체에서 일관된 애니메이션 경험을 제공합니다.
/// - Micro-interactions
/// - Page transitions
/// - Feedback animations
/// - Loading states
class AnimationUtils {
  AnimationUtils._();

  // ============ Common Animations ============

  /// 버튼 탭 애니메이션 (Material 3 Expressive)
  static Widget createTapAnimation({
    required Widget child,
    required VoidCallback onTap,
    double scale = AnimationConstants.microScale,
    Duration duration = AnimationConstants.fast,
    bool enableHaptics = true,
  }) {
    return AnimationWidgets.createTapAnimation(
      child: child,
      onTap: onTap,
      scale: scale,
      duration: duration,
      enableHaptics: enableHaptics,
    );
  }

  /// Fade In 애니메이션
  static Widget createFadeIn({
    required Widget child,
    Duration duration = AnimationConstants.medium,
    Curve curve = AnimationConstants.expressiveEase,
    double initialOpacity = 0.0,
    Offset? initialOffset,
  }) {
    return AnimationWidgets.createFadeIn(
      child: child,
      duration: duration,
      curve: curve,
      initialOpacity: initialOpacity,
      initialOffset: initialOffset,
    );
  }

  /// 리스트 아이템 등장 애니메이션
  static Widget createListItemAnimation({
    required Widget child,
    required int index,
    Duration duration = AnimationConstants.medium,
    Duration delay = AnimationConstants.listItemDelay,
  }) {
    return AnimationWidgets.createListItemAnimation(
      child: child,
      index: index,
      duration: duration,
      delay: delay,
    );
  }

  /// 카드 호버 애니메이션
  static Widget createCardHover({
    required Widget child,
    double hoverScale = AnimationConstants.defaultHoverScale,
    Duration duration = AnimationConstants.fast,
    double elevation = AnimationConstants.defaultElevation,
    double hoverElevation = AnimationConstants.defaultHoverElevation,
  }) {
    return AnimationWidgets.createCardHover(
      child: child,
      hoverScale: hoverScale,
      duration: duration,
      elevation: elevation,
      hoverElevation: hoverElevation,
    );
  }

  /// Shimmer 로딩 애니메이션
  static Widget createShimmer({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration duration = AnimationConstants.shimmerDuration,
  }) {
    return AnimationWidgets.createShimmer(
      child: child,
      baseColor: baseColor,
      highlightColor: highlightColor,
      duration: duration,
    );
  }

  // ============ Page Transition Builders ============

  /// Material 3 Expressive 페이지 전환
  static PageRouteBuilder<T> expressivePageRoute<T>({
    required Widget page,
    String? restorationId,
    Duration duration = AnimationConstants.medium,
    bool fullscreenDialog = false,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      fullscreenDialog: fullscreenDialog,
      settings: RouteSettings(name: restorationId),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _ExpressivePageTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
        );
      },
    );
  }

  /// 슬라이드 업 전환
  static PageRouteBuilder<T> slideUpPageRoute<T>({
    required Widget page,
    Duration duration = AnimationConstants.medium,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(
          tween.chain(CurveTween(curve: AnimationConstants.expressiveEase)),
        );

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }

  // ============ Loading Animations ============

  /// 펄스 로딩 애니메이션
  static Widget createPulseLoading({double size = AnimationConstants.defaultPulseSize, Color? color}) {
    return LoadingAnimations.createPulseLoading(size: size, color: color);
  }

  /// 회전 로딩 애니메이션
  static Widget createSpinningLoading({
    double size = AnimationConstants.defaultSpinSize,
    Color? color,
    double strokeWidth = AnimationConstants.defaultStrokeWidth,
  }) {
    return LoadingAnimations.createSpinningLoading(
      size: size,
      color: color,
      strokeWidth: strokeWidth,
    );
  }

  // 페이지 전환 애니메이션 설정
  static const Duration pageTransitionDuration = AnimationConstants.pageTransitionDuration;
  static const Curve pageTransitionCurve = AnimationConstants.pageTransitionCurve;

  // 페이지 전환 애니메이션
  static Widget pageTransition({
    required Widget child,
    required Animation<double> animation,
    bool slideUp = false,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final slideAnimation =
            Tween<Offset>(
              begin: slideUp ? const Offset(0.0, 0.3) : const Offset(0.3, 0.0),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(
                parent: animation,
                curve: Interval(0.0, 1.0, curve: pageTransitionCurve),
              ),
            );

        final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(0.0, 0.7, curve: pageTransitionCurve),
          ),
        );

        final scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(0.0, 1.0, curve: pageTransitionCurve),
          ),
        );

        return FadeTransition(
          opacity: fadeAnimation,
          child: SlideTransition(
            position: slideAnimation,
            child: Transform.scale(scale: scaleAnimation.value, child: child),
          ),
        );
      },
      child: child,
    );
  }

  // 모달 전환 애니메이션
  static Widget modalTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final slideAnimation =
            Tween<Offset>(
              begin: const Offset(0.0, 0.2),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(
                parent: animation,
                curve: Interval(0.0, 1.0, curve: pageTransitionCurve),
              ),
            );

        final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(0.0, 0.6, curve: pageTransitionCurve),
          ),
        );

        final scaleAnimation = Tween<double>(begin: 0.98, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(0.0, 1.0, curve: pageTransitionCurve),
          ),
        );

        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 5.0 * animation.value,
            sigmaY: 5.0 * animation.value,
          ),
          child: FadeTransition(
            opacity: fadeAnimation,
            child: SlideTransition(
              position: slideAnimation,
              child: Transform.scale(scale: scaleAnimation.value, child: child),
            ),
          ),
        );
      },
      child: child,
    );
  }

  // 다이얼로그 전환 애니메이션
  static Widget dialogTransition({
    required Animation<double> animation,
    required Widget child,
  }) {
    return ScaleTransition(
      scale: CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
      child: child,
    );
  }
}

// ============================================================================
// Private Implementation Classes
// ============================================================================

class _TapAnimationWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;
  final double scale;
  final Duration duration;
  final bool enableHaptics;

  const _TapAnimationWidget({
    required this.child,
    required this.onTap,
    required this.scale,
    required this.duration,
    required this.enableHaptics,
  });

  @override
  State<_TapAnimationWidget> createState() => _TapAnimationWidgetState();
}

class _TapAnimationWidgetState extends State<_TapAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    _scaleAnimation = Tween<double>(begin: 1.0, end: widget.scale).animate(
      CurvedAnimation(
        parent: _controller,
        curve: AnimationConstants.expressiveEaseIn,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(
      CurvedAnimation(
        parent: _controller,
        curve: AnimationConstants.expressiveEaseIn,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enableHaptics) {
      HapticFeedback.lightImpact();
    }
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _controller.reverse();
    widget.onTap();
  }

  void _handleTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: child,
              ),
            );
          },
          child: widget.child,
        ),
      ),
    );
  }
}

class _FadeInWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final double initialOpacity;
  final Offset? initialOffset;

  const _FadeInWidget({
    required this.child,
    required this.duration,
    required this.curve,
    required this.initialOpacity,
    this.initialOffset,
  });

  @override
  State<_FadeInWidget> createState() => _FadeInWidgetState();
}

class _FadeInWidgetState extends State<_FadeInWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Offset>? _offsetAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    _opacityAnimation = Tween<double>(
      begin: widget.initialOpacity,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    if (widget.initialOffset != null) {
      _offsetAnimation = Tween<Offset>(
        begin: widget.initialOffset!,
        end: Offset.zero,
      ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));
    }

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        Widget result = FadeTransition(
          opacity: _opacityAnimation,
          child: widget.child,
        );

        if (_offsetAnimation != null) {
          result = SlideTransition(position: _offsetAnimation!, child: result);
        }

        return result;
      },
    );
  }
}

class _ListItemAnimationWidget extends StatefulWidget {
  final Widget child;
  final int index;
  final Duration duration;
  final Duration delay;

  const _ListItemAnimationWidget({
    required this.child,
    required this.index,
    required this.duration,
    required this.delay,
  });

  @override
  State<_ListItemAnimationWidget> createState() =>
      _ListItemAnimationWidgetState();
}

class _ListItemAnimationWidgetState extends State<_ListItemAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: AnimationConstants.expressiveEase,
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0.0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _controller,
            curve: AnimationConstants.expressiveEase,
          ),
        );

    // 인덱스에 따른 지연
    Future.delayed(widget.delay * widget.index, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(position: _slideAnimation, child: child),
          );
        },
        child: widget.child,
      ),
    );
  }
}

class _CardHoverWidget extends StatefulWidget {
  final Widget child;
  final double hoverScale;
  final Duration duration;
  final double elevation;
  final double hoverElevation;

  const _CardHoverWidget({
    required this.child,
    required this.hoverScale,
    required this.duration,
    required this.elevation,
    required this.hoverElevation,
  });

  @override
  State<_CardHoverWidget> createState() => _CardHoverWidgetState();
}

class _CardHoverWidgetState extends State<_CardHoverWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    _scaleAnimation = Tween<double>(begin: 1.0, end: widget.hoverScale).animate(
      CurvedAnimation(
        parent: _controller,
        curve: AnimationConstants.expressiveEase,
      ),
    );

    _elevationAnimation =
        Tween<double>(
          begin: widget.elevation,
          end: widget.hoverElevation,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: AnimationConstants.expressiveEase,
          ),
        );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _controller.forward(),
      onExit: (_) => _controller.reverse(),
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Dimens.radiusM),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(
                      context,
                    ).colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: _elevationAnimation.value * 4,
                    spreadRadius: _elevationAnimation.value,
                  ),
                ],
              ),
              child: child,
            ),
          );
        },
        child: widget.child,
      ),
    );
  }
}

class _ShimmerWidget extends StatefulWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration duration;

  const _ShimmerWidget({
    required this.child,
    this.baseColor,
    this.highlightColor,
    required this.duration,
  });

  @override
  State<_ShimmerWidget> createState() => _ShimmerWidgetState();
}

class _ShimmerWidgetState extends State<_ShimmerWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..repeat();

    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.ease));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseColor =
        widget.baseColor ?? theme.colorScheme.surfaceContainerHighest;
    final highlightColor = widget.highlightColor ?? theme.colorScheme.surface;

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [baseColor, highlightColor, baseColor],
                stops: [
                  _animation.value - 0.3,
                  _animation.value,
                  _animation.value + 0.3,
                ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
              ).createShader(bounds);
            },
            child: child!,
          );
        },
        child: widget.child,
      ),
    );
  }
}

class _PulseLoadingWidget extends StatefulWidget {
  final double size;
  final Color? color;

  const _PulseLoadingWidget({required this.size, this.color});

  @override
  State<_PulseLoadingWidget> createState() => _PulseLoadingWidgetState();
}

class _PulseLoadingWidgetState extends State<_PulseLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationConstants.pulseDuration,
      vsync: this,
    )..repeat(reverse: true);

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _opacityAnimation = Tween<double>(
      begin: 0.4,
      end: 0.8,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).colorScheme.primary;

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: _opacityAnimation.value),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.2),
                    blurRadius: widget.size * 0.5 * _scaleAnimation.value,
                    spreadRadius: widget.size * 0.1 * _scaleAnimation.value,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class _SpinningLoadingWidget extends StatefulWidget {
  final double size;
  final Color? color;
  final double strokeWidth;

  const _SpinningLoadingWidget({
    required this.size,
    this.color,
    required this.strokeWidth,
  });

  @override
  State<_SpinningLoadingWidget> createState() => _SpinningLoadingWidgetState();
}

class _SpinningLoadingWidgetState extends State<_SpinningLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationConstants.spinDuration,
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2.0 * 3.14159).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutCubic),
    );

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.2), weight: 1),
      TweenSequenceItem(tween: Tween<double>(begin: 1.2, end: 1.0), weight: 1),
    ]).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).colorScheme.primary;

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: color, width: widget.strokeWidth),
                  gradient: SweepGradient(
                    colors: [color.withValues(alpha: 0.0), color.withValues(alpha: 0.8)],
                    stops: const [0.0, 0.8],
                    startAngle: 0.0,
                    endAngle: 3.14159 * 2,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _ExpressivePageTransition extends StatelessWidget {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Widget child;

  const _ExpressivePageTransition({
    required this.animation,
    required this.secondaryAnimation,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: SlideTransition(
        position: Tween<Offset>(begin: const Offset(1.0, 0.0), end: Offset.zero)
            .animate(
              CurvedAnimation(
                parent: animation,
                curve: AnimationConstants.expressiveEase,
              ),
            ),
        child: FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position:
                Tween<Offset>(
                  begin: Offset.zero,
                  end: const Offset(-0.3, 0.0),
                ).animate(
                  CurvedAnimation(
                    parent: secondaryAnimation,
                    curve: AnimationConstants.expressiveEase,
                  ),
                ),
            child: child,
          ),
        ),
      ),
    );
  }
}
