import '../services/database_service.dart';
import 'offline_task.dart';
import 'logger_utils.dart';

/// 오프라인 작업 큐의 자동 동기화/서버 연동/에러 복구를 관리하는 유틸리티 클래스입니다.
/// - 네트워크 상태 감지, 큐/DB/서버 동기화, 에러/재시도/복구 등 지원
/// - riverpod 3.x Provider/Repository와 연동, 오프라인/온라인 전환 UX 개선 목적
class OfflineTaskSyncManager {
  static bool _isSyncing = false;

  /// 모든 오프라인 작업을 동기화 (오프라인 DB 기준)
  static Future<void> syncAllTasks() async {
    if (_isSyncing) return;
    _isSyncing = true;
    try {
      final dbService = DatabaseServiceImpl(); // factory constructor가 싱글턴 반환
      final tasks = await dbService.getAllOfflineTasks();
      for (final map in tasks) {
        final task = OfflineTask.fromMap(map);
        try {
          await _applyTask(dbService, task);
          await dbService.removeOfflineTask(task.id);
        } catch (e, stack) {
          LoggerUtils.logError('오프라인 작업 동기화 실패', tag: 'OfflineTaskSyncManager', error: e, stackTrace: stack);
          // 실패 시 큐에 남겨두고, 다음 동기화 때 재시도
        }
      }
    } finally {
      _isSyncing = false;
    }
  }

  /// 실제 DB에 insert/update/delete 적용 (오프라인 DB 기준)
  static Future<void> _applyTask(
    DatabaseServiceImpl dbService,
    OfflineTask task,
  ) async {
    switch (task.type) {
      case OfflineTaskType.insert:
      case OfflineTaskType.create:  // create와 insert는 같은 처리
        await dbService.rawInsert(task.table, task.data);
        break;
      case OfflineTaskType.update:
        await dbService.rawUpdate(task.table, task.data);
        break;
      case OfflineTaskType.delete:
        await dbService.rawDelete(task.table, task.data);
        break;
      case OfflineTaskType.sync:
      case OfflineTaskType.upload:
      case OfflineTaskType.download:
        // 고급 동기화 작업은 기본 동기화 매니저에서 처리
        LoggerUtils.logInfo('고급 동기화 작업은 OfflineSyncManager에서 처리: ${task.type}', tag: 'OfflineTaskSyncManager');
        break;
    }
  }
}

// DatabaseServiceImpl에 rawInsert/rawUpdate/rawDelete 메서드가 필요합니다.
