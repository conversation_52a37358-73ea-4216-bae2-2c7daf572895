import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'logger_utils.dart';

/// 렌더링 성능 최적화 유틸리티
/// 
/// GPU 가속, RepaintBoundary 자동 적용, 렌더링 성능 모니터링 등을 제공합니다.
class RenderingOptimizer {
  static const String _tag = 'RenderingOptimizer';
  
  // 싱글톤 인스턴스
  static final RenderingOptimizer _instance = RenderingOptimizer._internal();
  factory RenderingOptimizer() => _instance;
  RenderingOptimizer._internal();

  // 성능 모니터링
  final List<Duration> _frameTimes = [];
  bool _isMonitoring = false;

  /// 렌더링 최적화 초기화
  static void initialize() {
    try {
      // GPU 가속 활성화
      _enableGPUAcceleration();
      
      // 렌더링 성능 모니터링 시작
      _instance._startPerformanceMonitoring();
      
      LoggerUtils.logInfo('Rendering optimization initialized', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to initialize rendering optimization', tag: _tag, error: e);
    }
  }

  /// GPU 가속 활성화
  static void _enableGPUAcceleration() {
    try {
      // 렌더링 엔진 최적화 설정 (deprecated API 사용 방지)
      // GPU 가속은 Flutter에서 기본적으로 활성화되어 있음
      LoggerUtils.logDebug('GPU acceleration is enabled by default', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('Failed to configure GPU acceleration', tag: _tag, error: e);
    }
  }

  /// RepaintBoundary 자동 적용 위젯
  static Widget autoRepaintBoundary({
    required Widget child,
    bool forceApply = false,
    String? debugLabel,
  }) {
    // 복잡한 위젯이나 자주 변경되는 위젯에 자동으로 RepaintBoundary 적용
    if (forceApply || _shouldApplyRepaintBoundary(child)) {
      return RepaintBoundary(
        child: child,
      );
    }
    return child;
  }

  /// RepaintBoundary 적용 여부 판단
  static bool _shouldApplyRepaintBoundary(Widget widget) {
    // 특정 위젯 타입들에 대해 RepaintBoundary 적용 권장
    return widget is ListView ||
           widget is GridView ||
           widget is CustomScrollView ||
           widget is AnimatedWidget ||
           widget is Image ||
           widget is Container && _hasComplexDecoration(widget);
  }

  /// 복잡한 데코레이션 여부 확인
  static bool _hasComplexDecoration(Container container) {
    final decoration = container.decoration;
    if (decoration is BoxDecoration) {
      return decoration.gradient != null ||
             decoration.boxShadow?.isNotEmpty == true ||
             decoration.image != null;
    }
    return false;
  }

  /// 최적화된 리스트 뷰 생성
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    double? cacheExtent,
    bool addRepaintBoundaries = true,
    bool addAutomaticKeepAlives = false,
    bool addSemanticIndexes = false,
  }) {
    return RepaintBoundary(
      child: ListView.builder(
        controller: controller,
        padding: padding,
        itemCount: itemCount,
        cacheExtent: cacheExtent ?? 500.0, // 기본 캐시 범위 설정
        addRepaintBoundaries: addRepaintBoundaries,
        addAutomaticKeepAlives: addAutomaticKeepAlives,
        addSemanticIndexes: addSemanticIndexes,
        itemBuilder: (context, index) {
          final item = itemBuilder(context, index);
          return addRepaintBoundaries ? RepaintBoundary(child: item) : item;
        },
      ),
    );
  }

  /// 최적화된 그리드 뷰 생성
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsets? padding,
    double? cacheExtent,
    bool addRepaintBoundaries = true,
    bool addAutomaticKeepAlives = false,
    bool addSemanticIndexes = false,
  }) {
    return RepaintBoundary(
      child: GridView.builder(
        controller: controller,
        padding: padding,
        itemCount: itemCount,
        gridDelegate: gridDelegate,
        cacheExtent: cacheExtent ?? 500.0,
        addRepaintBoundaries: addRepaintBoundaries,
        addAutomaticKeepAlives: addAutomaticKeepAlives,
        addSemanticIndexes: addSemanticIndexes,
        itemBuilder: (context, index) {
          final item = itemBuilder(context, index);
          return addRepaintBoundaries ? RepaintBoundary(child: item) : item;
        },
      ),
    );
  }

  /// 애니메이션 최적화 위젯
  static Widget optimizedAnimation({
    required Widget child,
    required Animation<double> animation,
    bool useRepaintBoundary = true,
  }) {
    final animatedChild = AnimatedBuilder(
      animation: animation,
      builder: (context, child) => child!,
      child: child,
    );

    return useRepaintBoundary 
        ? RepaintBoundary(child: animatedChild)
        : animatedChild;
  }

  /// 성능 모니터링 시작
  void _startPerformanceMonitoring() {
    if (_isMonitoring) return;

    // 개발 모드에서만 성능 모니터링 활성화 (릴리즈 모드에서는 비활성화)
    bool enableMonitoring = false;
    assert(() {
      enableMonitoring = true;
      return true;
    }());

    if (!enableMonitoring) {
      LoggerUtils.logDebug('Performance monitoring disabled in release mode', tag: _tag);
      return;
    }

    _isMonitoring = true;
    SchedulerBinding.instance.addTimingsCallback(_onFrameTimings);

    LoggerUtils.logDebug('Performance monitoring started', tag: _tag);
  }

  /// 프레임 타이밍 콜백
  void _onFrameTimings(List<FrameTiming> timings) {
    for (final timing in timings) {
      final frameDuration = timing.totalSpan;
      _frameTimes.add(frameDuration);

      // 60fps 기준 16.67ms를 초과하는 프레임 감지 (로그 레벨을 Debug로 변경)
      if (frameDuration.inMilliseconds > 16) {
        // Warning 대신 Debug 레벨로 변경하여 로그 출력 빈도 감소
        LoggerUtils.logDebug(
          'Slow frame detected: ${frameDuration.inMilliseconds}ms',
          tag: _tag,
        );
      }
    }

    // 최근 100개 프레임만 유지
    if (_frameTimes.length > 100) {
      _frameTimes.removeRange(0, _frameTimes.length - 100);
    }
  }

  /// 성능 통계 가져오기
  Map<String, dynamic> getPerformanceStats() {
    if (_frameTimes.isEmpty) {
      return {'message': 'No frame data available'};
    }

    final totalFrames = _frameTimes.length;
    final totalTime = _frameTimes.fold<int>(0, (sum, duration) => sum + duration.inMicroseconds);
    final averageFrameTime = totalTime / totalFrames / 1000; // ms
    
    final slowFrames = _frameTimes.where((duration) => duration.inMilliseconds > 16).length;
    final frameDropRate = (slowFrames / totalFrames * 100);
    
    return {
      'totalFrames': totalFrames,
      'averageFrameTime': averageFrameTime.toStringAsFixed(2),
      'slowFrames': slowFrames,
      'frameDropRate': frameDropRate.toStringAsFixed(2),
      'targetFPS': 60,
      'actualFPS': (1000 / averageFrameTime).toStringAsFixed(1),
    };
  }

  /// 성능 모니터링 중지
  void stopPerformanceMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    SchedulerBinding.instance.removeTimingsCallback(_onFrameTimings);
    
    LoggerUtils.logDebug('Performance monitoring stopped', tag: _tag);
  }

  /// 메모리 사용량 최적화
  static void optimizeMemoryUsage() {
    try {
      // 이미지 캐시 정리
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // 렌더 객체 정리 (필요시) - deprecated API 사용 방지
      // RendererBinding.instance.renderViews를 사용하지만 단순화
      for (final renderView in RendererBinding.instance.renderViews) {
        renderView.reassemble();
      }
      
      LoggerUtils.logDebug('Memory usage optimized', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Failed to optimize memory usage', tag: _tag, error: e);
    }
  }

  /// 복잡한 위젯을 위한 최적화된 컨테이너
  static Widget optimizedContainer({
    required Widget child,
    BoxDecoration? decoration,
    EdgeInsets? padding,
    EdgeInsets? margin,
    double? width,
    double? height,
    bool forceRepaintBoundary = false,
  }) {
    final container = Container(
      decoration: decoration,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      child: child,
    );

    // 복잡한 데코레이션이 있는 경우 RepaintBoundary 적용
    if (forceRepaintBoundary || _hasComplexDecoration(container)) {
      return RepaintBoundary(child: container);
    }

    return container;
  }

  /// 정기적인 성능 최적화 실행
  static void schedulePeriodicOptimization() {
    // 5분마다 메모리 최적화 실행
    Stream.periodic(const Duration(minutes: 5)).listen((_) {
      optimizeMemoryUsage();
    });
  }
}
