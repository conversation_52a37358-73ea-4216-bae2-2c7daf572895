import 'package:flutter/material.dart';
import '../../models/sales_stat_item.dart';
import '../../models/transaction_type.dart';
import '../../utils/app_colors.dart';
import '../../utils/currency_utils.dart';
import '../../utils/dimens.dart';
import 'statistics_state.dart';

/// 통계 화면의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 통계 카드 위젯들
/// - 필터 정보 표시 위젯들
/// - 통계 아이템 위젯들
/// - 현대적 Material Design 3 스타일 적용
class StatisticsUiComponents {
  /// 현대적인 필터 정보 카드
  static Widget buildModernFilterInfoCard({
    required BuildContext context,
    required String selectedSeller,
    required DateTimeRange? selectedDateRange,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.accent.withValues(alpha: 0.1),
            AppColors.accent.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        border: Border.all(
          color: AppColors.accent.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: AppColors.accent.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: Icon(
                  Icons.filter_alt_rounded,
                  color: AppColors.accent,
                  size: Dimens.iconSizeM,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                '적용된 필터',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.accent,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space12),

          if (selectedSeller != '전체 판매자') ...[
            _buildFilterChip(context, '판매자', selectedSeller, Icons.person_rounded),
            const SizedBox(height: Dimens.space8),
          ],

          if (selectedDateRange != null)
            _buildFilterChip(
              context,
              '기간',
              '${selectedDateRange.start.year}-${selectedDateRange.start.month.toString().padLeft(2, '0')}-${selectedDateRange.start.day.toString().padLeft(2, '0')} ~ ${selectedDateRange.end.year}-${selectedDateRange.end.month.toString().padLeft(2, '0')}-${selectedDateRange.end.day.toString().padLeft(2, '0')}',
              Icons.date_range_rounded,
            ),
        ],
      ),
    );
  }

  /// 필터 칩 위젯
  static Widget _buildFilterChip(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimens.space12,
        vertical: Dimens.space8,
      ),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(Dimens.radiusS),
        border: Border.all(color: AppColors.neutral30, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: Dimens.iconSizeS, color: AppColors.onSurfaceVariant),
          const SizedBox(width: Dimens.space8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 현대적인 전체 통계 카드
  static Widget buildModernOverallStatsCard({
    required BuildContext context,
    required SalesStats stats,
    required int totalPrepaymentAmount,
  }) {
    final salesAmount =
        stats.transactionTypeStats[TransactionType.sale]?.amount ?? 0;
    final discountAmount =
        stats.transactionTypeStats[TransactionType.discount]?.amount ?? 0;
    final netSalesAmount = salesAmount + discountAmount; // 할인은 음수로 저장됨
    final totalSettlementAmount = netSalesAmount + totalPrepaymentAmount;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.surface,
            AppColors.surfaceVariant.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(Dimens.space20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [AppColors.primarySeed, AppColors.primaryLight],
                    ),
                    borderRadius: BorderRadius.circular(Dimens.radiusM),
                  ),
                  child: Icon(
                    Icons.analytics_rounded,
                    color: AppColors.onPrimary,
                    size: Dimens.iconSizeL,
                  ),
                ),
                const SizedBox(width: Dimens.space16),
                Text(
                  '전체 통계',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: Dimens.space20),

            // 기본 통계 (현대적 그리드)
            Row(
              children: [
                Expanded(
                  child: _buildModernStatItem(
                    context,
                    '총 거래 건수',
                    '${stats.totalTransactions}건',
                    Icons.receipt_long_rounded,
                    AppColors.info,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: _buildModernStatItem(
                    context,
                    '총 수량',
                    '${stats.totalQuantity}개',
                    Icons.inventory_rounded,
                    AppColors.secondary,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: _buildModernStatItem(
                    context,
                    '총 금액',
                    CurrencyUtils.formatCurrency(stats.totalAmount),
                    Icons.account_balance_wallet_rounded,
                    AppColors.accent,
                  ),
                ),
              ],
            ),

            const SizedBox(height: Dimens.space20),
            Divider(color: AppColors.neutral30, thickness: 1),
            const SizedBox(height: Dimens.space20),

            // 세부 금액 정보 (현대적 리스트)
            _buildModernAmountRow(
              context,
              '현장 판매액',
              salesAmount,
              AppColors.success,
              Icons.point_of_sale_rounded,
            ),
            const SizedBox(height: Dimens.space12),
            _buildModernAmountRow(
              context,
              '할인 금액',
              discountAmount,
              AppColors.error,
              Icons.discount_rounded,
            ),
            const SizedBox(height: Dimens.space12),
            _buildModernAmountRow(
              context,
              '현장 순매출',
              netSalesAmount,
              AppColors.info,
              Icons.trending_up_rounded,
            ),
            const SizedBox(height: Dimens.space12),
            _buildModernAmountRow(
              context,
              '선입금 총액 (전체)',
              totalPrepaymentAmount,
              AppColors.warning,
              Icons.savings_rounded,
            ),

            const SizedBox(height: Dimens.space20),
            Divider(color: AppColors.neutral30, thickness: 1),
            const SizedBox(height: Dimens.space20),

            // 총 정산액 (강조 컨테이너)
            Container(
              padding: const EdgeInsets.all(Dimens.space16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primarySeed.withValues(alpha: 0.1),
                    AppColors.primaryLight.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(Dimens.radiusM),
                border: Border.all(
                  color: AppColors.primarySeed.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(Dimens.space8),
                    decoration: BoxDecoration(
                      color: AppColors.primarySeed,
                      borderRadius: BorderRadius.circular(Dimens.radiusS),
                    ),
                    child: Icon(
                      Icons.account_balance_rounded,
                      color: AppColors.onPrimary,
                      size: Dimens.iconSizeL,
                    ),
                  ),
                  const SizedBox(width: Dimens.space16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '총 정산액',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.onSurface,
                              ),
                        ),
                        const SizedBox(height: Dimens.space4),
                        Text(
                          CurrencyUtils.formatCurrency(totalSettlementAmount),
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primarySeed,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 현대적인 통계 아이템
  static Widget _buildModernStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(Dimens.radiusM),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(Dimens.radiusS),
            ),
            child: Icon(icon, color: color, size: Dimens.iconSizeM),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 현대적인 금액 행 위젯
  static Widget _buildModernAmountRow(
    BuildContext context,
    String label,
    int amount,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(Dimens.radiusS),
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(Dimens.radiusXS),
            ),
            child: Icon(icon, color: color, size: Dimens.iconSizeS),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
          ),
          Text(
            CurrencyUtils.formatCurrency(amount),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 현대적인 거래 유형별 통계 카드
  static Widget buildModernTransactionTypeStatsCard({
    required BuildContext context,
    required SalesStats stats,
  }) {
    return _buildModernCard(
      context: context,
      title: '거래 유형별 통계',
      icon: Icons.category_rounded,
      iconColor: AppColors.secondary,
      child: Column(
        children: stats.transactionTypeStats.entries.map((entry) {
          final type = entry.key;
          final typeStats = entry.value;
          final color = _getTransactionTypeColor(type);
          final icon = _getTransactionTypeIcon(type);

          return Container(
            margin: const EdgeInsets.only(bottom: Dimens.space8),
            padding: const EdgeInsets.all(Dimens.space12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(Dimens.radiusS),
              border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(Dimens.radiusXS),
                  ),
                  child: Icon(icon, color: color, size: Dimens.iconSizeS),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: Text(
                    _getTransactionTypeDisplayName(type),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${typeStats.count}건 • ${typeStats.quantity}개',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: Dimens.space2),
                    Text(
                      CurrencyUtils.formatCurrency(typeStats.amount),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 현대적인 상품별 통계 카드
  static Widget buildModernProductStatsCard({
    required BuildContext context,
    required SalesStats stats,
  }) {
    final sortedProducts = stats.productStats.entries.toList()
      ..sort((a, b) => b.value.amount.compareTo(a.value.amount));

    return _buildModernCard(
      context: context,
      title: '상품별 통계 (매출액 순)',
      icon: Icons.inventory_2_rounded,
      iconColor: AppColors.accent,
      child: Column(
        children: [
          ...sortedProducts.take(10).map((entry) {
            final productStats = entry.value;
            final salesStatItem = SalesStatItem(
              name: productStats.productName, // 이미 카테고리명이 포함된 상태
              count: productStats.count,
              totalAmount: productStats.amount.toDouble(),
            );
            return _buildProductStatItem(context, salesStatItem);
          }),
          if (sortedProducts.length > 10)
            Container(
              margin: const EdgeInsets.only(top: Dimens.space12),
              padding: const EdgeInsets.all(Dimens.space12),
              decoration: BoxDecoration(
                color: AppColors.neutral20,
                borderRadius: BorderRadius.circular(Dimens.radiusS),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.more_horiz_rounded,
                    color: AppColors.onSurfaceVariant,
                    size: Dimens.iconSizeS,
                  ),
                  const SizedBox(width: Dimens.space8),
                  Text(
                    '... 외 ${sortedProducts.length - 10}개 상품',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// 상품 통계 아이템
  static Widget _buildProductStatItem(
    BuildContext context,
    SalesStatItem productStats,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: Dimens.space8),
      padding: const EdgeInsets.all(Dimens.space12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(Dimens.radiusS),
        border: Border.all(color: AppColors.neutral30, width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space6),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.accent.withValues(alpha: 0.2),
                  AppColors.accent.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(Dimens.radiusXS),
            ),
            child: Icon(
              Icons.shopping_bag_rounded,
              color: AppColors.accent,
              size: Dimens.iconSizeS,
            ),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  productStats.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: Dimens.space2),
                Text(
                  '${productStats.count}건',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            CurrencyUtils.formatCurrency(productStats.totalAmount.toInt()),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.accent,
            ),
          ),
        ],
      ),
    );
  }

  /// 현대적인 판매자별 통계 카드
  static Widget buildModernSellerStatsCard({
    required BuildContext context,
    required SalesStats stats,
    required String selectedSeller,
  }) {
    if (selectedSeller != '전체 판매자') {
      return const SizedBox.shrink(); // 특정 판매자 선택 시 숨김
    }

    final sortedSellers = stats.sellerStats.entries.toList()
      ..sort((a, b) => b.value.amount.compareTo(a.value.amount));

    return _buildModernCard(
      context: context,
      title: '판매자별 통계 (매출액 순)',
      icon: Icons.people_rounded,
      iconColor: AppColors.info,
      child: Column(
        children: sortedSellers.map((entry) {
          final sellerStats = entry.value;
          final salesStatItem = SalesStatItem(
            name: sellerStats.sellerName,
            count: sellerStats.count,
            totalAmount: sellerStats.amount.toDouble(),
          );
          return _buildSellerStatItem(context, salesStatItem);
        }).toList(),
      ),
    );
  }

  /// 판매자 통계 아이템
  static Widget _buildSellerStatItem(
    BuildContext context,
    SalesStatItem sellerStats,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: Dimens.space8),
      padding: const EdgeInsets.all(Dimens.space12),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(Dimens.radiusS),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.info.withValues(alpha: 0.2),
                  AppColors.info.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(Dimens.radiusS),
            ),
            child: Icon(
              Icons.person_rounded,
              color: AppColors.info,
              size: Dimens.iconSizeM,
            ),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  sellerStats.name, // 판매자명
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: Dimens.space4),
                Text(
                  '${sellerStats.count}건',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            CurrencyUtils.formatCurrency(sellerStats.totalAmount.toInt()),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.info,
            ),
          ),
        ],
      ),
    );
  }

  /// 공통 현대적 카드 위젯
  static Widget _buildModernCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color iconColor,
    required Widget child,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(Dimens.space20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 카드 헤더
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        iconColor.withValues(alpha: 0.2),
                        iconColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(Dimens.radiusM),
                  ),
                  child: Icon(icon, color: iconColor, size: Dimens.iconSizeM),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),

            // 카드 콘텐츠
            child,
          ],
        ),
      ),
    );
  }

  /// 거래 유형 표시명
  static String _getTransactionTypeDisplayName(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return '판매';
      case TransactionType.service:
        return '서비스';
      case TransactionType.discount:
        return '할인';
      case TransactionType.serviceGift:
        return '서비스 증정';
      case TransactionType.setDiscount:
        return '세트 할인';
    }
  }

  /// 거래 유형별 색상 가져오기
  static Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return AppColors.success;
      case TransactionType.discount:
        return AppColors.error;
      case TransactionType.service:
        return AppColors.info;
      case TransactionType.serviceGift:
        return AppColors.secondary;
      case TransactionType.setDiscount:
        return Colors.green.shade600;
    }
  }

  /// 거래 유형별 아이콘 가져오기
  static IconData _getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Icons.point_of_sale_rounded;
      case TransactionType.discount:
        return Icons.discount_rounded;
      case TransactionType.service:
        return Icons.build_rounded;
      case TransactionType.serviceGift:
        return Icons.card_giftcard_rounded;
      case TransactionType.setDiscount:
        return Icons.local_offer_rounded;
    }
  }
} 