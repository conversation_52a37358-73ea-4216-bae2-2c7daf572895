import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/dimens.dart';
import 'statistics_controller.dart';
import 'statistics_ui_components.dart';

/// 통계 화면
///
/// 판매 통계를 조회하고 분석하는 화면입니다.
/// - 전체 통계, 거래 유형별 통계, 상품별 통계, 판매자별 통계 제공
/// - 필터링 기능 (판매자, 날짜 범위)
/// - 현대적 Material Design 3 UI
class StatisticsScreen extends ConsumerStatefulWidget {
  const StatisticsScreen({super.key});

  @override
  ConsumerState<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends ConsumerState<StatisticsScreen>
    with RestorationMixin {
  late StatisticsController _controller;

  @override
  String? get restorationId => 'statistics_screen';
  
  @override
  void initState() {
    super.initState();
    _controller = StatisticsController(
      ref: ref,
      context: context,
    );
    
    // 화면 진입 시 한 번만 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadInitialData();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 화면이 다시 포커스될 때 데이터 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _controller.refreshData();
      }
    });
  }

  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    try {
      await _controller.refreshData();
    } catch (e) {
      // 오류 발생 시 무시 (이미 Controller에서 처리됨)
    }
  }
  
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 로딩 상태 확인
    if (_controller.isLoading()) {
      return Scaffold(
        appBar: _buildAppBar(),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // 에러 상태 확인
    if (_controller.hasError()) {
      return Scaffold(
        appBar: _buildAppBar(),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Theme.of(context).colorScheme.error),
              const SizedBox(height: 16),
              Text('오류: ${_controller.getErrorMessage()}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _controller.refreshData(),
                child: const Text('다시 시도'),
              ),
            ],
          ),
        ),
      );
    }

    // 통계 계산
    final calculationResult = _controller.calculateStatistics();
    final stats = calculationResult['stats'];
    final totalPrepaymentAmount = calculationResult['totalPrepaymentAmount'];

    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () => _controller.refreshData(),
          child: RepaintBoundary(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(
                Dimens.getResponsivePadding(MediaQuery.of(context).size.width),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 필터 정보 표시
                  if (_controller.shouldShowFilterInfo())
                    StatisticsUiComponents.buildModernFilterInfoCard(
                      context: context,
                      selectedSeller: _controller.selectedSeller,
                      selectedDateRange: _controller.selectedDateRange,
                    ),

                if (_controller.shouldShowFilterInfo())
                  SizedBox(height: Dimens.space16),

                // 전체 통계 카드
                StatisticsUiComponents.buildModernOverallStatsCard(
                  context: context,
                  stats: stats,
                  totalPrepaymentAmount: totalPrepaymentAmount,
                ),
                SizedBox(height: Dimens.space16),

                // 거래 유형별 통계 카드
                StatisticsUiComponents.buildModernTransactionTypeStatsCard(
                  context: context,
                  stats: stats,
                ),
                SizedBox(height: Dimens.space16),

                // 상품별 통계 카드
                StatisticsUiComponents.buildModernProductStatsCard(
                  context: context,
                  stats: stats,
                ),
                SizedBox(height: Dimens.space16),

                // 판매자별 통계 카드
                if (_controller.shouldShowSellerStatsCard())
                  StatisticsUiComponents.buildModernSellerStatsCard(
                    context: context,
                    stats: stats,
                    selectedSeller: _controller.selectedSeller,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// AppBar 구성
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('통계'),
      centerTitle: true,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: Dimens.space8),
          child: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(Dimens.space8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(Dimens.radiusM),
              ),
              child: Icon(
                Icons.filter_list_rounded,
                color: Theme.of(context).colorScheme.onPrimary,
                size: Dimens.iconSizeM,
              ),
            ),
            onPressed: () => _controller.showFilterDialog(),
          ),
        ),
      ],
    );
  }
}
