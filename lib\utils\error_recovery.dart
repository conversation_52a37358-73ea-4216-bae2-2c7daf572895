import 'dart:async';
import 'dart:io';
import 'logger_utils.dart';
import 'provider_exception.dart';

/// 에러 복구 전략
enum ErrorRecoveryStrategy {
  /// 재시도
  retry,
  /// 기본값으로 복구
  fallbackToDefault,
  /// 캐시된 값 사용
  useCachedValue,
  /// 사용자에게 알림
  notifyUser,
  /// 무시
  ignore,
}

/// 에러 복구 정책
class ErrorRecoveryPolicy {
  final ErrorRecoveryStrategy strategy;
  final int maxRetries;
  final Duration retryDelay;
  final dynamic fallbackValue;
  final bool shouldLog;

  const ErrorRecoveryPolicy({
    required this.strategy,
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.fallbackValue,
    this.shouldLog = true,
  });

  static const ErrorRecoveryPolicy defaultPolicy = ErrorRecoveryPolicy(
    strategy: ErrorRecoveryStrategy.retry,
    maxRetries: 2,
    retryDelay: Duration(milliseconds: 500),
  );

  static const ErrorRecoveryPolicy networkErrorPolicy = ErrorRecoveryPolicy(
    strategy: ErrorRecoveryStrategy.retry,
    maxRetries: 3,
    retryDelay: Duration(seconds: 2),
  );

  static const ErrorRecoveryPolicy databaseErrorPolicy = ErrorRecoveryPolicy(
    strategy: ErrorRecoveryStrategy.fallbackToDefault,
    maxRetries: 1,
    retryDelay: Duration(milliseconds: 100),
  );
}

/// 에러 복구 관리자
class ErrorRecoveryManager {
  static const String _tag = 'ErrorRecoveryManager';
  static final Map<String, dynamic> _cache = {};
  static final Map<String, int> _retryCount = {};

  /// 에러 복구 실행
  static Future<T> recover<T>({
    required String operationKey,
    required Future<T> Function() operation,
    required ErrorRecoveryPolicy policy,
    String? tag,
  }) async {
    final currentRetries = _retryCount[operationKey] ?? 0;
    
    try {
      final result = await operation();
      
      // 성공 시 재시도 카운트 초기화
      _retryCount.remove(operationKey);
      
      // 결과 캐싱 (필요한 경우)
      if (policy.strategy == ErrorRecoveryStrategy.useCachedValue) {
        _cache[operationKey] = result;
      }
      
      return result;
    } catch (error, stackTrace) {
      if (policy.shouldLog) {
        LoggerUtils.logError(
          '작업 실패: $operationKey (시도 ${currentRetries + 1}/${policy.maxRetries + 1})',
          tag: tag ?? _tag,
          error: error,
          stackTrace: stackTrace,
        );
      }

      // 복구 전략 실행
      switch (policy.strategy) {
        case ErrorRecoveryStrategy.retry:
          return await _handleRetry<T>(
            operationKey: operationKey,
            operation: operation,
            policy: policy,
            currentRetries: currentRetries,
            tag: tag,
          );

        case ErrorRecoveryStrategy.fallbackToDefault:
          return await _handleFallback<T>(
            operationKey: operationKey,
            policy: policy,
            error: error,
            tag: tag,
          );

        case ErrorRecoveryStrategy.useCachedValue:
          return await _handleCachedValue<T>(
            operationKey: operationKey,
            policy: policy,
            error: error,
            tag: tag,
          );

        case ErrorRecoveryStrategy.notifyUser:
          return await _handleNotifyUser<T>(
            operationKey: operationKey,
            policy: policy,
            error: error,
            tag: tag,
          );

        case ErrorRecoveryStrategy.ignore:
          return await _handleIgnore<T>(
            operationKey: operationKey,
            policy: policy,
            error: error,
            tag: tag,
          );
      }
    }
  }

  /// 재시도 처리
  static Future<T> _handleRetry<T>({
    required String operationKey,
    required Future<T> Function() operation,
    required ErrorRecoveryPolicy policy,
    required int currentRetries,
    String? tag,
  }) async {
    if (currentRetries < policy.maxRetries) {
      _retryCount[operationKey] = currentRetries + 1;
      
      // 지연 후 재시도
      await Future.delayed(policy.retryDelay);
      
      LoggerUtils.logInfo(
        '재시도 중: $operationKey (${currentRetries + 1}/${policy.maxRetries})',
        tag: tag ?? _tag,
      );
      
      return await recover<T>(
        operationKey: operationKey,
        operation: operation,
        policy: policy,
        tag: tag,
      );
    } else {
      // 최대 재시도 횟수 초과
      _retryCount.remove(operationKey);
      
      if (policy.fallbackValue != null) {
        LoggerUtils.logWarning(
          '최대 재시도 횟수 초과, 기본값 사용: $operationKey',
          tag: tag ?? _tag,
        );
        return policy.fallbackValue as T;
      } else {
        LoggerUtils.logError(
          '최대 재시도 횟수 초과, 에러 전파: $operationKey',
          tag: tag ?? _tag,
        );
        throw Exception('최대 재시도 횟수 초과: $operationKey');
      }
    }
  }

  /// 기본값 복구 처리
  static Future<T> _handleFallback<T>({
    required String operationKey,
    required ErrorRecoveryPolicy policy,
    required dynamic error,
    String? tag,
  }) async {
    if (policy.fallbackValue != null) {
      LoggerUtils.logWarning(
        '기본값으로 복구: $operationKey',
        tag: tag ?? _tag,
      );
      return policy.fallbackValue as T;
    } else {
      LoggerUtils.logError(
        '기본값이 설정되지 않음, 에러 전파: $operationKey',
        tag: tag ?? _tag,
        error: error,
      );
      throw ProviderException.general(
        '기본값이 설정되지 않음: $operationKey',
        code: 'FALLBACK_VALUE_NOT_SET',
      );
    }
  }

  /// 캐시된 값 복구 처리
  static Future<T> _handleCachedValue<T>({
    required String operationKey,
    required ErrorRecoveryPolicy policy,
    required dynamic error,
    String? tag,
  }) async {
    final cachedValue = _cache[operationKey];
    if (cachedValue != null) {
      LoggerUtils.logWarning(
        '캐시된 값으로 복구: $operationKey',
        tag: tag ?? _tag,
      );
      return cachedValue as T;
    } else if (policy.fallbackValue != null) {
      LoggerUtils.logWarning(
        '캐시된 값 없음, 기본값 사용: $operationKey',
        tag: tag ?? _tag,
      );
      return policy.fallbackValue as T;
    } else {
      LoggerUtils.logError(
        '캐시된 값과 기본값 모두 없음, 에러 전파: $operationKey',
        tag: tag ?? _tag,
        error: error,
      );
      throw ProviderException.general(
        '캐시된 값과 기본값 모두 없음: $operationKey',
        code: 'CACHED_VALUE_AND_FALLBACK_NOT_SET',
      );
    }
  }

  /// 사용자 알림 처리
  static Future<T> _handleNotifyUser<T>({
    required String operationKey,
    required ErrorRecoveryPolicy policy,
    required dynamic error,
    String? tag,
  }) async {
    LoggerUtils.logError(
      '사용자 알림 필요: $operationKey',
      tag: tag ?? _tag,
      error: error,
    );

    // 사용자에게 알림 후 기본값 또는 에러 전파
    if (policy.fallbackValue != null) {
      return policy.fallbackValue as T;
    } else {
      throw ProviderException.general(
        '사용자 알림 후 기본값 없음: $operationKey',
        code: 'NOTIFY_USER_NO_FALLBACK',
      );
    }
  }

  /// 무시 처리
  static Future<T> _handleIgnore<T>({
    required String operationKey,
    required ErrorRecoveryPolicy policy,
    required dynamic error,
    String? tag,
  }) async {
    LoggerUtils.logDebug(
      '에러 무시: $operationKey',
      tag: tag ?? _tag,
    );

    if (policy.fallbackValue != null) {
      return policy.fallbackValue as T;
    } else {
      throw ProviderException.general(
        '에러가 무시되었지만 기본값이 설정되지 않음',
        code: 'ERROR_IGNORED_NO_FALLBACK',
      );
    }
  }

  /// 에러 타입별 정책 결정
  static ErrorRecoveryPolicy getPolicyForError(dynamic error) {
    if (error is SocketException || 
        error is TimeoutException ||
        error.toString().contains('network') ||
        error.toString().contains('connection')) {
      return ErrorRecoveryPolicy.networkErrorPolicy;
    }

    if (error.toString().contains('database') ||
        error.toString().contains('sqlite') ||
        error.toString().contains('sql')) {
      return ErrorRecoveryPolicy.databaseErrorPolicy;
    }

    return ErrorRecoveryPolicy.defaultPolicy;
  }

  /// 캐시 초기화
  static void clearCache([String? operationKey]) {
    if (operationKey != null) {
      _cache.remove(operationKey);
      _retryCount.remove(operationKey);
    } else {
      _cache.clear();
      _retryCount.clear();
    }
  }

  /// 캐시 상태 확인
  static bool hasCachedValue(String operationKey) {
    return _cache.containsKey(operationKey);
  }

  /// 재시도 횟수 확인
  static int getRetryCount(String operationKey) {
    return _retryCount[operationKey] ?? 0;
  }
} 