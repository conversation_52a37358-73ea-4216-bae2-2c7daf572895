import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../models/set_discount.dart';
import '../repositories/set_discount_repository.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';
import 'current_event_provider.dart';

part 'set_discount_provider.freezed.dart';

/// 세트 할인 상태를 관리하는 State 클래스
@freezed
abstract class SetDiscountState with _$SetDiscountState {
  const factory SetDiscountState({
    @Default([]) List<SetDiscount> setDiscounts,
    @Default([]) List<SetDiscount> filteredSetDiscounts,
    @Default(false) bool isLoading,
    @Default(false) bool isUpdating,
    String? errorMessage,
    @Default('') String searchQuery,
    @Default(true) bool showActiveOnly,
  }) = _SetDiscountState;
}

/// 세트 할인 Repository Provider
final setDiscountRepositoryProvider = Provider<SetDiscountRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SetDiscountRepository(database: databaseService);
});

/// 세트 할인 상태를 관리하는 StateNotifier
class SetDiscountNotifier extends StateNotifier<SetDiscountState> {
  final Ref ref;

  SetDiscountNotifier(this.ref) : super(const SetDiscountState());

  /// 세트 할인 목록 로드
  Future<void> loadSetDiscounts({bool showLoading = true}) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentEventIdProvider) ?? 1;
    
    if (showLoading) {
      state = state.copyWith(isLoading: true, errorMessage: null);
    }

    try {
      final setDiscounts = await repository.getAllSetDiscounts(eventId: currentEventId);
      state = state.copyWith(
        setDiscounts: setDiscounts,
        isLoading: false,
        errorMessage: null,
      );
      _applyFilters();
      
      LoggerUtils.logInfo('Loaded ${setDiscounts.length} set discounts', tag: 'SetDiscountProvider');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      LoggerUtils.logError('Failed to load set discounts', error: e, tag: 'SetDiscountProvider');
    }
  }

  /// 활성화된 세트 할인만 로드
  Future<List<SetDiscount>> getActiveSetDiscounts() async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentEventIdProvider) ?? 1;

    try {
      return await repository.getActiveSetDiscounts(eventId: currentEventId);
    } catch (e) {
      LoggerUtils.logError('Failed to get active set discounts', error: e, tag: 'SetDiscountProvider');
      return [];
    }
  }

  /// 세트 할인 추가
  Future<bool> addSetDiscount(SetDiscount setDiscount) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final id = await repository.insertSetDiscount(setDiscount);
      if (id != null) {
        await loadSetDiscounts(showLoading: false);
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount added successfully: $id', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 추가에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to add set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 수정
  Future<bool> updateSetDiscount(SetDiscount setDiscount) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final success = await repository.updateSetDiscount(setDiscount);
      if (success) {
        await loadSetDiscounts(showLoading: false);
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount updated successfully: ${setDiscount.id}', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 수정에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to update set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 삭제
  Future<bool> deleteSetDiscount(int id) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final success = await repository.deleteSetDiscount(id);
      if (success) {
        await loadSetDiscounts(showLoading: false);
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount deleted successfully: $id', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 삭제에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to delete set discount', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 세트 할인 활성화/비활성화
  Future<bool> toggleSetDiscountActive(int id, bool isActive) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    state = state.copyWith(isUpdating: true, errorMessage: null);

    try {
      final success = await repository.toggleSetDiscountActive(id, isActive);
      if (success) {
        await loadSetDiscounts(showLoading: false);
        state = state.copyWith(isUpdating: false);
        LoggerUtils.logInfo('Set discount active status changed: $id -> $isActive', tag: 'SetDiscountProvider');
        return true;
      } else {
        state = state.copyWith(isUpdating: false, errorMessage: '세트 할인 상태 변경에 실패했습니다.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isUpdating: false, errorMessage: e.toString());
      LoggerUtils.logError('Failed to toggle set discount active', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 검색어 설정
  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFilters();
  }

  /// 활성화 필터 토글
  void toggleShowActiveOnly() {
    state = state.copyWith(showActiveOnly: !state.showActiveOnly);
    _applyFilters();
  }

  /// 필터 적용
  void _applyFilters() {
    var filtered = state.setDiscounts;

    // 활성화 필터
    if (state.showActiveOnly) {
      filtered = filtered.where((discount) => discount.isActive).toList();
    }

    // 검색 필터
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((discount) =>
        discount.name.toLowerCase().contains(query)
      ).toList();
    }

    state = state.copyWith(filteredSetDiscounts: filtered);
  }

  /// 세트 할인 이름 중복 확인
  Future<bool> isNameExists(String name, {int? excludeId}) async {
    final repository = ref.read(setDiscountRepositoryProvider);
    final currentEventId = ref.read(currentEventIdProvider) ?? 1;

    try {
      return await repository.isNameExists(name, eventId: currentEventId, excludeId: excludeId);
    } catch (e) {
      LoggerUtils.logError('Failed to check name exists', error: e, tag: 'SetDiscountProvider');
      return false;
    }
  }

  /// 에러 메시지 클리어
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// 세트 할인 Provider
final setDiscountNotifierProvider = StateNotifierProvider<SetDiscountNotifier, SetDiscountState>((ref) {
  return SetDiscountNotifier(ref);
});

/// 필터링된 세트 할인 목록 Provider
final filteredSetDiscountsProvider = Provider<List<SetDiscount>>((ref) {
  return ref.watch(setDiscountNotifierProvider).filteredSetDiscounts;
});

/// 활성화된 세트 할인 목록 Provider (판매 시 사용)
final activeSetDiscountsProvider = FutureProvider<List<SetDiscount>>((ref) async {
  final notifier = ref.read(setDiscountNotifierProvider.notifier);
  return await notifier.getActiveSetDiscounts();
});

/// 로딩 상태 Provider
final setDiscountLoadingProvider = Provider<bool>((ref) {
  return ref.watch(setDiscountNotifierProvider).isLoading;
});

/// 업데이트 상태 Provider
final setDiscountUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(setDiscountNotifierProvider).isUpdating;
});

/// 에러 메시지 Provider
final setDiscountErrorProvider = Provider<String?>((ref) {
  return ref.watch(setDiscountNotifierProvider).errorMessage;
});
