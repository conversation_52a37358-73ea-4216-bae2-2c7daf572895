import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../utils/network_optimizer.dart';
import '../utils/offline_task_sync_manager.dart';

/// 네트워크 상태 정보
class NetworkStatusState {
  final bool isOnline;
  final NetworkState networkState;
  final ConnectivityResult connectivityResult;
  final DateTime lastUpdated;
  final bool isInitialized;
  final Map<String, dynamic> stats;

  const NetworkStatusState({
    required this.isOnline,
    required this.networkState,
    required this.connectivityResult,
    required this.lastUpdated,
    this.isInitialized = false,
    this.stats = const {},
  });

  NetworkStatusState copyWith({
    bool? isOnline,
    NetworkState? networkState,
    ConnectivityResult? connectivityResult,
    DateTime? lastUpdated,
    bool? isInitialized,
    Map<String, dynamic>? stats,
  }) {
    return NetworkStatusState(
      isOnline: isOnline ?? this.isOnline,
      networkState: networkState ?? this.networkState,
      connectivityResult: connectivityResult ?? this.connectivityResult,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isInitialized: isInitialized ?? this.isInitialized,
      stats: stats ?? this.stats,
    );
  }
}

/// 네트워크 상태 StateNotifier
class NetworkStatusNotifier extends StateNotifier<NetworkStatusState> {
  StreamSubscription? _connectivitySubscription;
  NetworkOptimizer? _networkOptimizer;

  NetworkStatusNotifier()
      : super(NetworkStatusState(
          isOnline: true,
          networkState: NetworkState.unknown,
          connectivityResult: ConnectivityResult.none,
          lastUpdated: DateTime.now(),
          isInitialized: false,
        ));

  Future<void> initialize() async {
    if (state.isInitialized) return;
    LoggerUtils.logInfo('네트워크 상태 관리자 초기화 시작', tag: 'NetworkStatusProvider');
    try {
      _networkOptimizer = NetworkOptimizer(
        config: NetworkOptimizerConfig.defaultConfig(),
      );
      await _networkOptimizer!.initialize();
      await _setupConnectivityListener();
      await _checkInitialConnectivity();
      state = state.copyWith(
        isInitialized: true,
        lastUpdated: DateTime.now(),
      );
      LoggerUtils.logInfo('네트워크 상태 관리자 초기화 완료', tag: 'NetworkStatusProvider');
    } catch (e) {
      LoggerUtils.logError(
        '네트워크 상태 관리자 초기화 실패',
        error: e,
        tag: 'NetworkStatusProvider',
      );
      rethrow;
    }
  }

  Future<void> _setupConnectivityListener() async {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      // 첫 번째 결과를 사용 (일반적으로 하나의 연결 상태만 있음)
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _onConnectivityChanged(result);
    });
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      final results = await Connectivity().checkConnectivity();
      // checkConnectivity는 List<ConnectivityResult>를 반환
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _onConnectivityChanged(result);
    } catch (e) {
      LoggerUtils.logError(
        '초기 연결 상태 확인 실패',
        error: e,
        tag: 'NetworkStatusProvider',
      );
    }
  }

  void _onConnectivityChanged(ConnectivityResult result) {
    final isOnline = result != ConnectivityResult.none;
    final networkState = _convertToNetworkState(result);
    final previousState = state;
    final newState = state.copyWith(
      isOnline: isOnline,
      networkState: networkState,
      connectivityResult: result,
      lastUpdated: DateTime.now(),
    );
    state = newState;
    if (previousState.isOnline != isOnline) {
      LoggerUtils.logInfo(
        '네트워크 상태 변경:  ${previousState.isOnline ? '온라인' : '오프라인'} → ${isOnline ? '온라인' : '오프라인'}',
        tag: 'NetworkStatusProvider',
      );
      if (isOnline) {
        _triggerOfflineSync();
      }
    }
    _networkOptimizer?.onNetworkStateChanged?.call(networkState);
  }

  NetworkState _convertToNetworkState(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return NetworkState.wifi;
      case ConnectivityResult.mobile:
        return NetworkState.mobile;
      case ConnectivityResult.none:
        return NetworkState.disconnected;
      default:
        return NetworkState.connected;
    }
  }

  void _triggerOfflineSync() {
    try {
      OfflineTaskSyncManager.syncAllTasks();
      LoggerUtils.logDebug(
        '오프라인 작업 동기화 트리거됨',
        tag: 'NetworkStatusProvider',
      );
    } catch (e) {
      LoggerUtils.logError(
        '오프라인 작업 동기화 트리거 실패',
        error: e,
        tag: 'NetworkStatusProvider',
      );
    }
  }

  NetworkOptimizer? get networkOptimizer => _networkOptimizer;

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _networkOptimizer?.dispose();
    super.dispose();
  }
}

final networkStatusProvider = StateNotifierProvider<NetworkStatusNotifier, NetworkStatusState>(
  (ref) => NetworkStatusNotifier(),
);

/// 네트워크 최적화 상태 StateNotifier
class NetworkOptimizationState {
  final bool isInitialized;
  final int totalRequests;
  final int cachedResponses;
  final int batchRequests;
  final int deduplicatedRequests;
  final int cacheSize;
  final int maxCacheSize;
  final double cacheHitRate;
  final double averageResponseTime;
  final Map<String, dynamic> config;

  const NetworkOptimizationState({
    this.isInitialized = false,
    this.totalRequests = 0,
    this.cachedResponses = 0,
    this.batchRequests = 0,
    this.deduplicatedRequests = 0,
    this.cacheSize = 0,
    this.maxCacheSize = 100,
    this.cacheHitRate = 0.0,
    this.averageResponseTime = 0.0,
    this.config = const {},
  });

  NetworkOptimizationState copyWith({
    bool? isInitialized,
    int? totalRequests,
    int? cachedResponses,
    int? batchRequests,
    int? deduplicatedRequests,
    int? cacheSize,
    int? maxCacheSize,
    double? cacheHitRate,
    double? averageResponseTime,
    Map<String, dynamic>? config,
  }) {
    return NetworkOptimizationState(
      isInitialized: isInitialized ?? this.isInitialized,
      totalRequests: totalRequests ?? this.totalRequests,
      cachedResponses: cachedResponses ?? this.cachedResponses,
      batchRequests: batchRequests ?? this.batchRequests,
      deduplicatedRequests: deduplicatedRequests ?? this.deduplicatedRequests,
      cacheSize: cacheSize ?? this.cacheSize,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      cacheHitRate: cacheHitRate ?? this.cacheHitRate,
      averageResponseTime: averageResponseTime ?? this.averageResponseTime,
      config: config ?? this.config,
    );
  }
}

class NetworkOptimizationNotifier extends StateNotifier<NetworkOptimizationState> {
  Timer? _statsUpdateTimer;
  NetworkOptimizer? _networkOptimizer;

  NetworkOptimizationNotifier() : super(const NetworkOptimizationState());

  Future<void> initialize(NetworkOptimizer networkOptimizer) async {
    if (state.isInitialized) return;
    _networkOptimizer = networkOptimizer;
    _startStatsUpdateTimer();
    state = state.copyWith(
      isInitialized: true,
      config: _networkOptimizer!.config.toJson(),
    );
    LoggerUtils.logInfo('네트워크 최적화 상태 관리자 초기화 완료', tag: 'NetworkOptimizationProvider');
  }

  void _startStatsUpdateTimer() {
    _statsUpdateTimer?.cancel();
    _statsUpdateTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _updateStats();
    });
  }

  void _updateStats() {
    if (_networkOptimizer == null) return;
    final stats = _networkOptimizer!.getStats();
    state = state.copyWith(
      totalRequests: stats['totalRequests'] ?? 0,
      cachedResponses: stats['cachedResponses'] ?? 0,
      batchRequests: stats['batchRequests'] ?? 0,
      deduplicatedRequests: stats['deduplicatedRequests'] ?? 0,
      cacheSize: stats['cacheSize'] ?? 0,
      maxCacheSize: stats['maxCacheSize'] ?? 100,
      cacheHitRate: stats['cacheHitRate'] ?? 0.0,
      averageResponseTime: stats['averageResponseTime'] ?? 0.0,
    );
  }

  void invalidateCache([String? pattern]) {
    _networkOptimizer?.invalidateCache(pattern);
    LoggerUtils.logInfo(
      '캐시 무효화: ${pattern ?? '전체'}',
      tag: 'NetworkOptimizationProvider',
    );
  }

  @override
  void dispose() {
    _statsUpdateTimer?.cancel();
    super.dispose();
  }
}

final networkOptimizationProvider = StateNotifierProvider<NetworkOptimizationNotifier, NetworkOptimizationState>(
  (ref) => NetworkOptimizationNotifier(),
);

/// 네트워크 상태 관련 선택적 Provider들
final isOnlineProvider = Provider<bool>((ref) {
  return ref.watch(networkStatusProvider).isOnline;
});

final networkStateProvider = Provider<NetworkState>((ref) {
  return ref.watch(networkStatusProvider).networkState;
});

final connectivityResultProvider = Provider<ConnectivityResult>((ref) {
  return ref.watch(networkStatusProvider).connectivityResult;
}); 