import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../repositories/settings_repository.dart';
import '../../utils/provider_error_handler.dart';
import '../base_state.dart';

/// 디스플레이 설정 상태
class DisplaySettingsState extends BaseState {
  final int inventoryColumnsPortrait;
  final int inventoryColumnsLandscape;
  final int saleColumnsPortrait;
  final int saleColumnsLandscape;

  const DisplaySettingsState({
    required this.inventoryColumnsPortrait,
    required this.inventoryColumnsLandscape,
    required this.saleColumnsPortrait,
    required this.saleColumnsLandscape,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  DisplaySettingsState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  DisplaySettingsState copyWith({
    int? inventoryColumnsPortrait,
    int? inventoryColumnsLandscape,
    int? saleColumnsPortrait,
    int? saleColumnsLandscape,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return DisplaySettingsState(
      inventoryColumnsPortrait: inventoryColumnsPortrait ?? this.inventoryColumnsPortrait,
      inventoryColumnsLandscape: inventoryColumnsLandscape ?? this.inventoryColumnsLandscape,
      saleColumnsPortrait: saleColumnsPortrait ?? this.saleColumnsPortrait,
      saleColumnsLandscape: saleColumnsLandscape ?? this.saleColumnsLandscape,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    inventoryColumnsPortrait,
    inventoryColumnsLandscape,
    saleColumnsPortrait,
    saleColumnsLandscape,
  ];
}

/// 디스플레이 설정 Notifier
class DisplaySettingsNotifier extends StateNotifier<DisplaySettingsState> {
  static const String _tag = 'DisplaySettingsNotifier';
  final SettingsRepository _repository;

  DisplaySettingsNotifier(this._repository) : super(const DisplaySettingsState(
    inventoryColumnsPortrait: 3,
    inventoryColumnsLandscape: 5,
    saleColumnsPortrait: 2,
    saleColumnsLandscape: 4,
  )) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    await ProviderErrorHandler.executeWithErrorHandling<void, DisplaySettingsState>(
      operation: () async {
        final inventoryPortrait = await _repository.getInt('inventory_columns_portrait') ?? 3;
        final inventoryLandscape = await _repository.getInt('inventory_columns_landscape') ?? 5;
        final salePortrait = await _repository.getInt('sale_columns_portrait') ?? 2;
        final saleLandscape = await _repository.getInt('sale_columns_landscape') ?? 4;

        state = state.copyWith(
          inventoryColumnsPortrait: inventoryPortrait,
          inventoryColumnsLandscape: inventoryLandscape,
          saleColumnsPortrait: salePortrait,
          saleColumnsLandscape: saleLandscape,
        );
      },
      notifier: this,
      errorCode: 'DISPLAY_LOAD_ERROR',
      tag: _tag,
      operationName: '디스플레이 설정 로드',
    );
  }

  Future<void> updateInventoryColumnsPortrait(int columns) async {
    await ProviderErrorHandler.executeWithErrorHandling<void, DisplaySettingsState>(
      operation: () async {
        await _repository.setInt('inventory_columns_portrait', columns);
        state = state.copyWith(inventoryColumnsPortrait: columns);
      },
      notifier: this,
      errorCode: 'INVENTORY_PORTRAIT_UPDATE_ERROR',
      tag: _tag,
      operationName: '세로 재고 컬럼 수 업데이트',
    );
  }

  Future<void> updateInventoryColumnsLandscape(int columns) async {
    await ProviderErrorHandler.executeWithErrorHandling<void, DisplaySettingsState>(
      operation: () async {
        await _repository.setInt('inventory_columns_landscape', columns);
        state = state.copyWith(inventoryColumnsLandscape: columns);
      },
      notifier: this,
      errorCode: 'INVENTORY_LANDSCAPE_UPDATE_ERROR',
      tag: _tag,
      operationName: '가로 재고 컬럼 수 업데이트',
    );
  }

  Future<void> updateSaleColumnsPortrait(int columns) async {
    await ProviderErrorHandler.executeWithErrorHandling<void, DisplaySettingsState>(
      operation: () async {
        await _repository.setInt('sale_columns_portrait', columns);
        state = state.copyWith(saleColumnsPortrait: columns);
      },
      notifier: this,
      errorCode: 'SALE_PORTRAIT_UPDATE_ERROR',
      tag: _tag,
      operationName: '세로 판매 컬럼 수 업데이트',
    );
  }

  Future<void> updateSaleColumnsLandscape(int columns) async {
    await ProviderErrorHandler.executeWithErrorHandling<void, DisplaySettingsState>(
      operation: () async {
        await _repository.setInt('sale_columns_landscape', columns);
        state = state.copyWith(saleColumnsLandscape: columns);
      },
      notifier: this,
      errorCode: 'SALE_LANDSCAPE_UPDATE_ERROR',
      tag: _tag,
      operationName: '가로 판매 컬럼 수 업데이트',
    );
  }
}

/// 디스플레이 설정 Provider
final displaySettingsNotifierProvider = StateNotifierProvider<DisplaySettingsNotifier, DisplaySettingsState>((ref) {
  final repository = SettingsRepository();
  return DisplaySettingsNotifier(repository);
});

/// 개별 설정값 Provider들 (성능 최적화)
final inventoryColumnsPortraitProvider = Provider<int>((ref) {
  return ref.watch(displaySettingsNotifierProvider.select((state) => state.inventoryColumnsPortrait));
});

final inventoryColumnsLandscapeProvider = Provider<int>((ref) {
  return ref.watch(displaySettingsNotifierProvider.select((state) => state.inventoryColumnsLandscape));
});

final saleColumnsPortraitProvider = Provider<int>((ref) {
  return ref.watch(displaySettingsNotifierProvider.select((state) => state.saleColumnsPortrait));
});

final saleColumnsLandscapeProvider = Provider<int>((ref) {
  return ref.watch(displaySettingsNotifierProvider.select((state) => state.saleColumnsLandscape));
}); 