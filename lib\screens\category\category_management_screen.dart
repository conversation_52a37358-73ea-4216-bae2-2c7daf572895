import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/category.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../widgets/confirmation_dialog.dart';

/// 카테고리 관리 화면
/// 드래그 앤 드롭으로 순서 변경 가능
class CategoryManagementScreen extends ConsumerStatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  ConsumerState<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends ConsumerState<CategoryManagementScreen> {
  final TextEditingController _categoryNameController = TextEditingController();
  List<Category> _reorderableCategories = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCategories();
    });
  }

  @override
  void dispose() {
    _categoryNameController.dispose();
    super.dispose();
  }

  /// 카테고리 목록 로드
  void _loadCategories() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace != null) {
      ref.read(categoryNotifierProvider.notifier).loadCategories(eventId: currentWorkspace.id);
    }
  }

  /// 새 카테고리 추가 다이얼로그
  void _showAddCategoryDialog() async {
    final controller = TextEditingController();
    String? error;

    try {
      final result = await showDialog<String>(
        context: context,
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: const Text('새 카테고리 추가'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: '카테고리 이름을 입력하세요',
                    errorText: error,
                  ),
                  autofocus: true,
                  onSubmitted: (value) {
                    if (value.trim().isEmpty) {
                      setState(() => error = '카테고리 이름을 입력해주세요.');
                      return;
                    }
                    if (mounted) {
                      Navigator.of(context).pop(value.trim());
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                },
                child: const Text('취소'),
              ),
              ElevatedButton(
                onPressed: () {
                  final value = controller.text.trim();
                  if (value.isEmpty) {
                    setState(() => error = '카테고리 이름을 입력해주세요.');
                    return;
                  }
                  if (mounted) {
                    Navigator.of(context).pop(value);
                  }
                },
                child: const Text('추가'),
              ),
            ],
          ),
        ),
      );

      if (result != null && result.isNotEmpty) {
        await _addCategoryWithName(result);
      }
    } finally {
      // 다이얼로그가 완전히 닫힌 후에 컨트롤러를 안전하게 dispose
      controller.dispose();
    }
  }

  /// 카테고리 이름 수정 다이얼로그
  void _showEditCategoryDialog(Category category) async {
    final controller = TextEditingController(text: category.name);
    String? error;

    try {
      final result = await showDialog<String>(
        context: context,
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: const Text('카테고리 이름 수정'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: '카테고리 이름을 입력하세요',
                    errorText: error,
                  ),
                  autofocus: true,
                  onSubmitted: (value) {
                    if (value.trim().isEmpty) {
                      setState(() => error = '카테고리 이름을 입력해주세요.');
                      return;
                    }
                    if (mounted) {
                      Navigator.of(context).pop(value.trim());
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                },
                child: const Text('취소'),
              ),
              ElevatedButton(
                onPressed: () {
                  final value = controller.text.trim();
                  if (value.isEmpty) {
                    setState(() => error = '카테고리 이름을 입력해주세요.');
                    return;
                  }
                  if (mounted) {
                    Navigator.of(context).pop(value);
                  }
                },
                child: const Text('수정'),
              ),
            ],
          ),
        ),
      );

      if (result != null && result.isNotEmpty) {
        await _updateCategoryWithName(category.id!, result);
      }
    } finally {
      // 다이얼로그가 완전히 닫힌 후에 컨트롤러를 안전하게 dispose
      controller.dispose();
    }
  }

  /// 카테고리 삭제 확인 다이얼로그
  void _showDeleteConfirmDialog(Category category) {
    ConfirmationDialog.showDelete(
      context: context,
      title: '카테고리 삭제',
      message: '"${category.name}" 카테고리를 삭제하시겠습니까?\n\n카테고리에 상품이 있으면 삭제할 수 없습니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
      onConfirm: () {
        _deleteCategory(category.id!);
      },
    );
  }

  /// 카테고리 추가
  Future<void> _addCategoryWithName(String name) async {
    // 현재 워크스페이스 ID 가져오기
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('현재 워크스페이스가 선택되지 않았습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final success = await ref.read(categoryNotifierProvider.notifier).addCategory(
      name: name,
      eventId: currentWorkspace.id,
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('카테고리가 추가되었습니다.')),
      );
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error ?? '카테고리 추가에 실패했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 카테고리 수정
  Future<void> _updateCategoryWithName(int id, String name) async {
    final success = await ref.read(categoryNotifierProvider.notifier).updateCategory(
      id: id,
      name: name,
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('카테고리가 수정되었습니다.')),
      );
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error ?? '카테고리 수정에 실패했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 카테고리 삭제
  Future<void> _deleteCategory(int id) async {
    final success = await ref.read(categoryNotifierProvider.notifier).deleteCategory(id);
    
    if (success) {
      _showSnackBar('카테고리가 삭제되었습니다.');
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      _showSnackBar(error ?? '카테고리 삭제에 실패했습니다.');
    }
  }

  /// 카테고리 순서 재정렬
  Future<void> _reorderCategories(int oldIndex, int newIndex) async {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final Category item = _reorderableCategories.removeAt(oldIndex);
      _reorderableCategories.insert(newIndex, item);
    });

    final success = await ref.read(categoryNotifierProvider.notifier).reorderCategories(_reorderableCategories);
    
    if (!success) {
      _showSnackBar('카테고리 순서 변경에 실패했습니다.');
      // 실패시 원래 순서로 되돌리기
      _loadCategories();
    }
  }

  /// 스낵바 표시
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoryNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('카테고리 관리'),
      ),
      body: categoriesAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, _) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '카테고리 로드 중 오류가 발생했습니다.\n$error',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCategories,
                child: const Text('다시 시도'),
              ),
            ],
          ),
        ),
        data: (categories) {
          _reorderableCategories = List.from(categories);
          
          if (categories.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.category,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '등록된 카테고리가 없습니다.\n아래 버튼을 눌러 새 카테고리를 추가해보세요.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 카테고리 목록
              Expanded(
                child: ReorderableListView.builder(
                  itemCount: categories.length,
                  onReorder: _reorderCategories,
                  buildDefaultDragHandles: false, // 기본 드래그 핸들 비활성화
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return Card(
                      key: ValueKey(category.id),
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      child: ListTile(
                        leading: const Icon(Icons.category),
                        title: Text(
                          category.name,
                          style: TextStyle(fontFamily: 'Pretendard', 
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text('순서: ${category.sortOrder + 1}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () => _showEditCategoryDialog(category),
                              tooltip: '수정',
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _showDeleteConfirmDialog(category),
                              tooltip: '삭제',
                            ),
                            ReorderableDragStartListener(
                              index: index,
                              child: const Icon(
                                Icons.drag_handle,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCategoryDialog,
        tooltip: '새 카테고리 추가',
        child: const Icon(Icons.add),
      ),
    );
  }
}


