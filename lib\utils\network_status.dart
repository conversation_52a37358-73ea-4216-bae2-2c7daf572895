import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';

import 'offline_task_sync_manager.dart';

/// 네트워크 상태 감지 및 오프라인/온라인 전환 처리를 지원하는 유틸리티 클래스입니다.
/// - riverpod 3.x Provider/Repository와 연동, 오프라인 작업 큐/동기화 등 지원
/// - 네트워크 장애/복구, 자동 동기화, UX 개선 목적
class NetworkStatusUtil {
  static bool _isOnline = true; // 기본값을 온라인으로 설정
  static final _listeners = <Function(bool)>[];
  static StreamSubscription? _subscription;

  /// 네트워크 상태 모니터링 시작
  static void startMonitoring() {
    _subscription?.cancel();
    _subscription = Connectivity().onConnectivityChanged.listen((result) {
      final isOnline = result != ConnectivityResult.none;
      if (_isOnline != isOnline) {
        _isOnline = isOnline;
        _notifyListeners();
        if (_isOnline) {
          OfflineTaskSyncManager.syncAllTasks();
        }
      }
    });

    // 초기 상태 확인
    Connectivity().checkConnectivity().then((result) {
      final isOnline = result != ConnectivityResult.none;
      if (_isOnline != isOnline) {
        _isOnline = isOnline;
        _notifyListeners();
        if (_isOnline) {
          OfflineTaskSyncManager.syncAllTasks();
        }
      }
    });
  }

  /// 네트워크 상태 모니터링 중지
  static void stopMonitoring() {
    _subscription?.cancel();
    _subscription = null;
  }

  /// 모든 리소스 정리 (앱 종료 시 호출)
  static void dispose() {
    stopMonitoring();
    _listeners.clear();
  }

  /// 네트워크 상태 변경 리스너 등록
  static void addListener(Function(bool) listener) {
    if (!_listeners.contains(listener)) {
      _listeners.add(listener);
    }
  }

  /// 네트워크 상태 변경 리스너 제거
  static void removeListener(Function(bool) listener) {
    _listeners.remove(listener);
  }

  /// 현재 네트워크 연결 상태 반환
  static bool get isOnline => _isOnline;

  /// 리스너들에게 상태 변경 알림
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener(_isOnline);
    }
  }

  /// 현재 네트워크 연결 타입 확인
  static dynamic checkConnectivity() {
    return Connectivity().checkConnectivity();
  }

  /// 테스트용: 네트워크 상태 강제 변경
  static void setOnline(bool value) {
    _isOnline = value;
  }
}
