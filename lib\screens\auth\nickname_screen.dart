import 'package:flutter/material.dart';
import '../../providers/nickname_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../widgets/onboarding_components.dart';

/// 닉네임 설정 페이지 - 웜톤 디자인으로 개선
///
/// 웜톤 색상과 개선된 아바타 디자인을 적용한 현대적 닉네임 설정 경험
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class NicknameScreen extends ConsumerStatefulWidget {
  final VoidCallback onNicknameSet;
  const NicknameScreen({super.key, required this.onNicknameSet});

  @override
  ConsumerState<NicknameScreen> createState() => _NicknameScreenState();
}

class _NicknameScreenState extends ConsumerState<NicknameScreen>
    with TickerProviderStateMixin {
  final nicknameController = TextEditingController();
  String? error;
  bool isLoading = false;
  bool isCheckingDuplicate = false;
  bool? isDuplicateChecked; // null: 미확인, true: 사용가능, false: 중복
  String? lastCheckedNickname;

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();

    // 텍스트 필드 변경 감지
    nicknameController.addListener(_onNicknameChanged);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 애니메이션 시작
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    nicknameController.removeListener(_onNicknameChanged);
    nicknameController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// 닉네임 입력 변경 시 중복 체크 상태 리셋
  void _onNicknameChanged() {
    final currentNickname = nicknameController.text.trim();
    setState(() {
      if (lastCheckedNickname != null && lastCheckedNickname != currentNickname) {
        isDuplicateChecked = null;
        lastCheckedNickname = null;
        error = null;
      }
    });
  }

  /// 닉네임 중복 체크
  Future<void> _checkNicknameDuplicate() async {
    final nickname = nicknameController.text.trim();
    if (nickname.isEmpty) {
      setState(() { error = '닉네임을 입력하세요.'; });
      return;
    }

    setState(() {
      isCheckingDuplicate = true;
      error = null;
      isDuplicateChecked = null;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('로그인이 필요합니다.');
      }

      // Firestore에서 같은 닉네임을 사용하는 다른 사용자가 있는지 확인
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .where('nickname', isEqualTo: nickname)
          .get();

      // 현재 사용자가 아닌 다른 사용자가 같은 닉네임을 사용하는지 확인
      final isDuplicate = querySnapshot.docs.any((doc) => doc.id != currentUser.uid);

      setState(() {
        isDuplicateChecked = !isDuplicate;
        lastCheckedNickname = nickname;
        // error 메시지 설정 제거 - _buildDuplicateCheckStatus에서만 표시
      });
    } catch (e) {
      setState(() {
        error = '중복 확인 실패: $e';
        isDuplicateChecked = null;
      });
    } finally {
      setState(() { isCheckingDuplicate = false; });
    }
  }

  Future<void> _saveNickname() async {
    setState(() { isLoading = true; error = null; });
    final nickname = nicknameController.text.trim();
    if (nickname.isEmpty) {
      setState(() { error = '닉네임을 입력하세요.'; isLoading = false; });
      return;
    }

    // 중복 체크가 되지 않았거나 실패한 경우
    if (isDuplicateChecked != true || lastCheckedNickname != nickname) {
      setState(() {
        error = '닉네임 중복 확인을 먼저 해주세요.';
        isLoading = false;
      });
      return;
    }

    try {
      await ref.read(nicknameProvider.notifier).setNickname(nickname);
      await ref.read(nicknameProvider.notifier).loadNickname(); // 닉네임 재로드
      widget.onNicknameSet();
    } catch (e) {
      setState(() { error = '닉네임 저장 실패: $e'; });
    }
    setState(() { isLoading = false; });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true, // 키보드 처리 개선
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: constraints.maxHeight * 0.1, // 상하 여백을 화면 크기에 비례하게
                    ),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight * 0.8, // 최소 높이 보장
                      ),
                      child: IntrinsicHeight(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            OnboardingComponents.buildCard(
                              context: context,
                              child: _buildNicknameForm(context),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 닉네임 설정 폼 구성
  Widget _buildNicknameForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 헤더
        _buildHeader(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 닉네임 입력 필드 (중복 확인 버튼 포함)
        _buildNicknameInput(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 중복 체크 상태 메시지
        _buildDuplicateCheckStatus(context),

        // 에러 메시지
        if (error != null) _buildErrorMessage(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 저장 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '시작하기',
          onPressed: _canSave() ? _handleSave : null,
          isLoading: isLoading,
          icon: Icons.arrow_forward,
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 도움말 텍스트
        _buildHelpText(context),
      ],
    );
  }

  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 아바타 아이콘
        _buildAvatarIcon(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '닉네임을 설정해주세요',
        ),

        const SizedBox(height: 8),

        // 부제목
        OnboardingComponents.buildSubtitle(
          context: context,
          text: '파라바라에서 사용할\n판매자 이름을 입력해주세요',
        ),
      ],
    );
  }

  /// 아바타 아이콘
  Widget _buildAvatarIcon(BuildContext context) {
    final iconSize = ResponsiveHelper.getMainIconSize(context) * 0.8;

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        gradient: OnboardingColors.primaryGradient,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 1.5),
        boxShadow: [
          BoxShadow(
            color: OnboardingColors.primary.withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 배경 원
          Container(
            width: iconSize * 0.7,
            height: iconSize * 0.7,
            decoration: BoxDecoration(
              color: OnboardingColors.accentLight,
              shape: BoxShape.circle,
            ),
          ),
          // 사용자 아이콘
          Icon(
            Icons.person_rounded,
            size: iconSize * 0.4,
            color: OnboardingColors.textOnPrimary,
          ),
          // 편집 아이콘
          Positioned(
            right: iconSize * 0.05,
            bottom: iconSize * 0.05,
            child: Container(
              width: iconSize * 0.25,
              height: iconSize * 0.25,
              decoration: BoxDecoration(
                color: OnboardingColors.accent,
                shape: BoxShape.circle,
                border: Border.all(color: OnboardingColors.surface, width: 2),
              ),
              child: Icon(
                Icons.edit,
                size: iconSize * 0.12,
                color: OnboardingColors.textOnPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 닉네임 입력 필드 (중복 확인 버튼 포함)
  Widget _buildNicknameInput(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 닉네임 텍스트 필드
        Expanded(
          child: OnboardingComponents.buildTextField(
            context: context,
            controller: nicknameController,
            label: '닉네임',
            prefixIcon: Icons.person_outline,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _canCheckDuplicate() ? _checkNicknameDuplicate() : null,
          ),
        ),

        const SizedBox(width: 12),

        // 중복 확인 버튼 (아웃라인 스타일)
        Container(
          height: ResponsiveHelper.getButtonHeight(context),
          child: OutlinedButton(
            onPressed: _canCheckDuplicate() ? _checkNicknameDuplicate : null,
            style: OutlinedButton.styleFrom(
              foregroundColor: _canCheckDuplicate() ? OnboardingColors.primary : OnboardingColors.textTertiary,
              side: BorderSide(
                color: _canCheckDuplicate() ? OnboardingColors.primary : OnboardingColors.secondary,
                width: 1.5,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
              ),
              backgroundColor: Colors.transparent,
            ),
            child: isCheckingDuplicate
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(OnboardingColors.primary),
                    ),
                  )
                : Text(
                    '중복확인',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }



  /// 중복 체크 상태 표시
  Widget _buildDuplicateCheckStatus(BuildContext context) {
    if (isDuplicateChecked == null) {
      return const SizedBox.shrink();
    }

    final isAvailable = isDuplicateChecked == true;
    final color = isAvailable ? Colors.green : Colors.red;
    final icon = isAvailable ? Icons.check_circle : Icons.error;
    final text = isAvailable ? '사용 가능한 닉네임입니다' : '이미 사용 중인 닉네임입니다';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 에러 메시지
  Widget _buildErrorMessage(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: OnboardingColors.errorLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: OnboardingColors.error.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: OnboardingColors.error, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error!,
              style: TextStyle(
                color: OnboardingColors.error,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 도움말 텍스트
  Widget _buildHelpText(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: OnboardingColors.infoLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        border: Border.all(color: OnboardingColors.info.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: OnboardingColors.info,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '닉네임은 나중에 설정에서 변경할 수 있습니다',
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                color: OnboardingColors.info,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 중복 체크 가능 여부 확인
  bool _canCheckDuplicate() {
    return !isCheckingDuplicate && !isLoading && nicknameController.text.trim().isNotEmpty;
  }

  /// 저장 가능 여부 확인
  bool _canSave() {
    final nickname = nicknameController.text.trim();
    return !isLoading &&
           !isCheckingDuplicate &&
           nickname.isNotEmpty &&
           isDuplicateChecked == true &&
           lastCheckedNickname == nickname;
  }

  /// 저장 처리
  void _handleSave() {
    _saveNickname();
  }
}