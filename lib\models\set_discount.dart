import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'set_discount.freezed.dart';
part 'set_discount.g.dart';

/// 세트 할인 정보를 표현하는 데이터 모델 클래스입니다.
/// - 세트명, 할인 금액, 포함된 상품 ID들 등 다양한 속성 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
/// - freezed를 사용하여 불변 객체로 생성
@freezed
abstract class SetDiscount with _$SetDiscount {
  const factory SetDiscount({
    int? id,
    required String name,
    required int discountAmount,
    required List<int> productIds,
    @Default(true) bool isActive,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(1) int eventId, // 행사 ID 추가
  }) = _SetDiscount;

  factory SetDiscount.fromJson(Map<String, dynamic> json) => _$SetDiscountFromJson(json);

  // SQLite 맵에서 직접 생성
  factory SetDiscount.fromMap(Map<String, dynamic> map) {
    // productIds는 JSON 문자열로 저장되므로 파싱 필요
    List<int> productIds = [];
    if (map['productIds'] != null) {
      try {
        final productIdsJson = map['productIds'] as String;
        final List<dynamic> productIdsList = 
            (jsonDecode(productIdsJson) as List<dynamic>);
        productIds = productIdsList.map((e) => e as int).toList();
      } catch (e) {
        // 파싱 실패 시 빈 리스트
        productIds = [];
      }
    }

    return SetDiscount(
      id: map['id'],
      name: map['name'] ?? '',
      discountAmount: map['discountAmount'] ?? 0,
      productIds: productIds,
      isActive: map['isActive'] == 1 || map['isActive'] == true,
      createdAt: map['createdAt'] != null 
          ? DateTime.parse(map['createdAt']) 
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.parse(map['updatedAt']) 
          : null,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory SetDiscount.create({
    int? id,
    required String name,
    required int discountAmount,
    required List<int> productIds,
    bool isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    required int eventId, // 행사 ID 필수로 변경
  }) {
    return SetDiscount(
      id: id,
      name: name,
      discountAmount: discountAmount,
      productIds: productIds,
      isActive: isActive,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt,
      eventId: eventId,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension SetDiscountMapper on SetDiscount {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'discountAmount': discountAmount,
      'productIds': jsonEncode(productIds), // JSON 문자열로 저장
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'eventId': eventId,
    };
  }
}
