/// Blue Booth Manager - 카테고리 데이터 모델
///
/// 상품 카테고리 정보를 표현하는 데이터 모델 클래스입니다.
/// - 카테고리명, 정렬 순서, 행사 ID 등 포함
/// - 실시간 동기화 지원
/// - 불변 객체 패턴 (immutable)
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'sync_metadata.dart';

part 'category.freezed.dart';
part 'category.g.dart';

/// 카테고리 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class Category with _$Category {
  const factory Category({
    int? id,
    required String name,
    @Default(0) int sortOrder,
    @Default(1) int eventId, // 행사 ID

    // 실시간 동기화 메타데이터
    SyncMetadata? syncMetadata,
  }) = _Category;

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);

  /// 기본 카테고리 생성
  factory Category.createDefault({required int eventId}) {
    return Category(
      name: '기본 카테고리',
      sortOrder: 0,
      eventId: eventId,
    );
  }
}

extension CategoryExtensions on Category {
  /// 데이터베이스 저장용 Map 변환 (id 제외)
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'name': name,
      'sortOrder': sortOrder,
      'eventId': eventId,
    };
    
    // 동기화 메타데이터를 JSON 문자열로 저장 (안전한 방식)
    if (syncMetadata != null) {
      try {
        map['syncMetadata'] = jsonEncode(syncMetadata!.toJson());
      } catch (e) {
        // JSON 인코딩 실패 시 null로 저장하고 로그 출력
        print('Category toMap: syncMetadata JSON 인코딩 실패: $e');
        map['syncMetadata'] = null;
      }
    } else {
      map['syncMetadata'] = null;
    }
    
    return map;
  }

  /// id를 포함한 Map 변환
  Map<String, dynamic> toMapWithId() {
    final map = toMap();
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  /// 실시간 동기화를 위한 업데이트된 카테고리 생성
  Category withSyncUpdate({
    String? deviceId,
    SyncStatus? syncStatus,
  }) {
    final updatedMetadata = syncMetadata != null
        ? SyncMetadata.updated(
            syncMetadata!,
            deviceId: deviceId,
            newStatus: syncStatus,
          )
        : SyncMetadata.create(deviceId: deviceId);

    return copyWith(syncMetadata: updatedMetadata);
  }

  /// 새 카테고리 생성시 동기화 메타데이터 포함
  Category withSyncMetadata({String? deviceId}) {
    return copyWith(
      syncMetadata: SyncMetadata.create(deviceId: deviceId),
    );
  }
}
