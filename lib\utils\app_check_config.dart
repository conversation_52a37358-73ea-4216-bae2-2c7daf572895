/// Firebase App Check 환경 설정
/// 
/// 개발/프로덕션 환경에 따른 App Check 설정을 관리합니다.
library;

import 'dart:io';
import 'package:firebase_app_check/firebase_app_check.dart';

class AppCheckConfig {
  // 환경 설정
  static const bool isDebugMode = true; // 프로덕션 배포 시 false로 변경
  
  // 주의: 디버그 토큰은 하드코딩하지 않습니다.
  // Firebase가 자동으로 생성한 실제 토큰을 로그에서 확인하고 Firebase 콘솔에 등록하세요.
  
  /// 현재 환경에 맞는 Android Provider 반환
  static AndroidProvider get androidProvider {
    return isDebugMode 
        ? AndroidProvider.debug 
        : AndroidProvider.playIntegrity;
  }
  
  /// 현재 환경에 맞는 Apple Provider 반환
  static AppleProvider get appleProvider {
    return isDebugMode 
        ? AppleProvider.debug 
        : AppleProvider.appAttest;
  }
  
  /// 디버그 토큰 안내 메시지 반환
  static String get currentDebugToken {
    if (Platform.isAndroid) {
      return 'Android 디버그 토큰이 로그에 자동 출력됩니다. 로그에서 "Firebase App Check debug token" 를 찾아보세요.';
    } else if (Platform.isIOS) {
      return 'iOS 디버그 토큰이 로그에 자동 출력됩니다. 로그에서 "Firebase App Check debug token" 를 찾아보세요.';
    } else {
      return 'Unknown Platform';
    }
  }
  
  /// 현재 설정 정보 출력용
  static Map<String, dynamic> get configInfo {
    return {
      'isDebugMode': isDebugMode,
      'platform': Platform.isAndroid ? 'Android' : Platform.isIOS ? 'iOS' : 'Unknown',
      'androidProvider': isDebugMode ? 'Debug' : 'Play Integrity',
      'appleProvider': isDebugMode ? 'Debug' : 'App Attest',
      'debugToken': isDebugMode ? '로그에서 자동 생성된 토큰을 확인하세요' : 'N/A (Production)',
    };
  }
}
