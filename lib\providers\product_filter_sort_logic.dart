import '../models/product.dart';
import '../models/product_sort_option.dart';

/// 상품 필터링 및 정렬 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 상품 목록 필터링
/// - 상품 목록 정렬
/// - 실시간 상태 관리
class ProductFilterSortLogic {
  /// 상품 목록에 필터/정렬을 적용합니다.
  ///
  /// [products]: 전체 상품 리스트
  /// [sellerFilter]: 판매자 필터(선택)
  /// [sortOption]: 정렬 옵션(선택)
  /// 반환값: 필터/정렬된 Product 리스트
  static List<Product> applyFiltersAndSort(
    List<Product> products, {
    String? sellerFilter,
    ProductSortOption? sortOption,
  }) {
    if (products.isEmpty) return [];

    final filterToApply = sellerFilter ?? '';
    final sortOptionToApply = sortOption ?? ProductSortOption.recentlyAdded;

    // 필터링 적용
    var filteredProducts = products;
    if (filterToApply.isNotEmpty) {
      filteredProducts = products.where((p) => p.sellerName == filterToApply).toList();
    }

    // 정렬 적용
    filteredProducts.sort((a, b) => _compareProducts(a, b, sortOptionToApply));
    return filteredProducts;
  }

  /// 상품 비교 함수(정렬용)
  ///
  /// [a], [b]: 비교할 Product 객체
  /// [sortOption]: 정렬 옵션
  /// 반환값: 비교 결과(-1, 0, 1)
  static int _compareProducts(Product a, Product b, ProductSortOption sortOption) {
    switch (sortOption) {
      case ProductSortOption.nameAsc:
        return a.name.compareTo(b.name);
      case ProductSortOption.nameDesc:
        return b.name.compareTo(a.name);
      case ProductSortOption.quantityAsc:
        return a.quantity.compareTo(b.quantity);
      case ProductSortOption.quantityDesc:
        return b.quantity.compareTo(a.quantity);
      case ProductSortOption.priceAsc:
        return a.price.compareTo(b.price);
      case ProductSortOption.priceDesc:
        return b.price.compareTo(a.price);
      case ProductSortOption.sellerAsc:
        final sellerA = a.sellerName ?? '';
        final sellerB = b.sellerName ?? '';
        return sellerA.compareTo(sellerB);
      case ProductSortOption.sellerDesc:
        final sellerA = a.sellerName ?? '';
        final sellerB = b.sellerName ?? '';
        return sellerB.compareTo(sellerA);
      case ProductSortOption.recentlyAdded:
        final idA = a.id ?? 0;
        final idB = b.id ?? 0;
        return idB.compareTo(idA);
    }
  }

  /// 상품이 필터 조건과 매치되는지 확인
  ///
  /// [product]: 확인할 상품
  /// [filters]: 필터 조건 맵
  /// 반환값: 필터 조건과 매치 여부
  static bool matchesProductFilters(Product product, Map<String, dynamic> filters) {
    // 판매자 필터
    if (filters.containsKey('sellerName')) {
      final sellerName = filters['sellerName'] as String;
      if (sellerName.isNotEmpty && product.sellerName != sellerName) {
        return false;
      }
    }

    // 가격 범위 필터
    if (filters.containsKey('minPrice')) {
      final minPrice = filters['minPrice'] as int;
      if (product.price < minPrice) {
        return false;
      }
    }

    if (filters.containsKey('maxPrice')) {
      final maxPrice = filters['maxPrice'] as int;
      if (product.price > maxPrice) {
        return false;
      }
    }

    // 수량 범위 필터
    if (filters.containsKey('minQuantity')) {
      final minQuantity = filters['minQuantity'] as int;
      if (product.quantity < minQuantity) {
        return false;
      }
    }

    if (filters.containsKey('maxQuantity')) {
      final maxQuantity = filters['maxQuantity'] as int;
      if (product.quantity > maxQuantity) {
        return false;
      }
    }

    return true;
  }

  /// 상품 목록에서 판매자 이름 목록을 추출합니다.
  ///
  /// [products]: 상품 목록
  /// 반환값: 정렬된 판매자 이름 목록
  static List<String> extractSellerNames(List<Product> products) {
    return products.map((p) => p.sellerName ?? '').where((name) => name.isNotEmpty).toSet().toList()..sort();
  }

  /// 필터링된 상품 목록을 생성합니다.
  ///
  /// [products]: 전체 상품 목록
  /// [filters]: 필터 조건
  /// 반환값: 필터링된 상품 목록
  static List<Product> filterProducts(
    List<Product> products,
    Map<String, dynamic> filters,
  ) {
    if (filters.isEmpty) return List.from(products);

    return products.where((product) => matchesProductFilters(product, filters)).toList();
  }

  /// 정렬된 상품 목록을 생성합니다.
  ///
  /// [products]: 상품 목록
  /// [sortOption]: 정렬 옵션
  /// 반환값: 정렬된 상품 목록
  static List<Product> sortProducts(
    List<Product> products,
    ProductSortOption sortOption,
  ) {
    if (products.isEmpty) return [];

    final sortedProducts = List<Product>.from(products);
    sortedProducts.sort((a, b) => _compareProducts(a, b, sortOption));
    return sortedProducts;
  }

  /// 필터링과 정렬을 순차적으로 적용합니다.
  ///
  /// [products]: 전체 상품 목록
  /// [filters]: 필터 조건
  /// [sortOption]: 정렬 옵션
  /// 반환값: 필터링 및 정렬된 상품 목록
  static List<Product> filterAndSortProducts(
    List<Product> products, {
    Map<String, dynamic>? filters,
    ProductSortOption? sortOption,
  }) {
    final filterParams = filters ?? <String, dynamic>{};
    final sortParams = sortOption ?? ProductSortOption.recentlyAdded;

    // 먼저 필터링
    final filteredProducts = filterProducts(products, filterParams);

    // 그 다음 정렬
    return sortProducts(filteredProducts, sortParams);
  }
} 