---
type: "always_apply"
---

- 항상 코드베이스와 유기적인 연결, 흐름을 분석하고 파악하여 문제없는 코딩 하기.
- 기능 추가 및 수정 시, 이미 구현된 다른 기능에 영향을 주지 않도록 하기.
- 사용자 직접 명령이 없다면 기존 기능이나 UI/UX는 임의로 변화하지않기. 추가,삭제,변경 전부 금지.
- UI는 모바일과 타블렛, 가로와 세로모드 전부 동적으로 문제없게 구현하기.
- 작업이 끝나면 flutter analyze로 문제 확인 거치기. 빌드할땐 무조건 flutter run 기본명령어만 쓰기. 핫 리로드 하지않기.
- 안정성을 유지하는 범위 내에서 최대한 병렬로 작업하기. 특 히 한 파일에 대한 작업은 되도록 한번에 전부 적용하기.
- 모든 페이지는 Safe Area로 적용하기.
- 새로 데이터가 생성되거나 수정되는경우, 행사별 관리 여부나 파이어베이스 동기화에 대해 문제 여부를 파악하고 올바르게 적용하기.
- 모든 데이터 변경에는 UI 갱신이 제대로 작동해야함.
- 작업에 필요한 패키지/라이브러리는 언제든 추가 설치하기.
- 안드로이드와 IOS 둘 다 제대로 지원하도록 코딩할것.
- 단순한 방식 혹은 빠른 문제 해결을 지향하지말고, 무조건 완성도를 최우선으로 할것.
Sequential thinking
-문제가 생긴 기능을 비활성화하는것으로 해결하려 하지 말것.