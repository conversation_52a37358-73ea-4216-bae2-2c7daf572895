// import 'package:firebase_app_check/firebase_app_check.dart'; // 개발 중 비활성화
import 'logger_utils.dart';

/// Firebase 서비스 사용 전 App Check 준비 상태를 확인하는 래퍼 클래스
class FirebaseServiceWrapper {
  static const Duration _tokenTimeout = Duration(seconds: 5);


  /// Firebase 서비스 호출 전에 App Check 토큰이 준비되었는지 확인 (단순화)
  static Future<bool> ensureAppCheckReady() async {
    try {
      LoggerUtils.logDebug('🚫 개발 모드: App Check 완전 비활성화, 바로 진행', tag: 'FirebaseWrapper');
      
      // App Check 비활성화 상태에서는 바로 true 반환
      return true;

      /*
      // 단일 시도로 변경 (Too many attempts 오류 방지)
      try {
        final token = await FirebaseAppCheck.instance.getToken()
            .timeout(_tokenTimeout);

        if (token != null && token.isNotEmpty) {
          LoggerUtils.logDebug('App Check 토큰 확인 완료', tag: 'FirebaseWrapper');
          return true;
        } else {
          LoggerUtils.logDebug('App Check 토큰이 준비되지 않았지만 계속 진행', tag: 'FirebaseWrapper');
          return true; // 토큰이 없어도 계속 진행
        }
      } catch (e) {
        LoggerUtils.logDebug('App Check 토큰 확인 실패, 계속 진행: $e', tag: 'FirebaseWrapper');
        return true; // 오류가 있어도 계속 진행
      }
      */
    } catch (e) {
      LoggerUtils.logError('App Check 준비 상태 확인 중 오류 발생', tag: 'FirebaseWrapper', error: e);
      return true; // 오류가 있어도 계속 진행
    }
  }

  /// Firebase 서비스 호출을 App Check 준비 상태 확인과 함께 실행
  static Future<T?> executeWithAppCheck<T>(
    Future<T> Function() firebaseOperation,
    String operationName,
  ) async {
    try {
      // App Check 준비 상태 확인
      final isReady = await ensureAppCheckReady();
      
      if (!isReady) {
        LoggerUtils.logWarning('App Check 준비되지 않음. $operationName 계속 진행', tag: 'FirebaseWrapper');
      }
      
      // Firebase 작업 실행
      LoggerUtils.logDebug('$operationName 실행 중...', tag: 'FirebaseWrapper');
      final result = await firebaseOperation();
      LoggerUtils.logDebug('$operationName 완료', tag: 'FirebaseWrapper');
      
      return result;
    } catch (e) {
      LoggerUtils.logError('$operationName 실행 중 오류 발생', tag: 'FirebaseWrapper', error: e);
      rethrow;
    }
  }

  /// App Check 토큰 강제 갱신 (개발 중 비활성화)
  static Future<void> refreshAppCheckToken() async {
    try {
      LoggerUtils.logInfo('🚫 개발 모드: App Check 토큰 갱신 비활성화', tag: 'FirebaseWrapper');
      return; // App Check 비활성화 상태에서는 아무것도 하지 않음
      
      /*
      LoggerUtils.logInfo('App Check 토큰 강제 갱신 시작', tag: 'FirebaseWrapper');
      
      // 현재 토큰 상태 확인
      final token = await FirebaseAppCheck.instance.getToken();
      
      if (token != null && token.isNotEmpty) {
        LoggerUtils.logInfo('App Check 토큰 강제 갱신 완료', tag: 'FirebaseWrapper');
      } else {
        LoggerUtils.logWarning('App Check 토큰 갱신 후에도 토큰이 없음', tag: 'FirebaseWrapper');
      }
      */
    } catch (e) {
      LoggerUtils.logError('App Check 토큰 갱신 실패', tag: 'FirebaseWrapper', error: e);
    }
  }
}
