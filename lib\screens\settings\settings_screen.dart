import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/settings_provider.dart';

import '../../utils/logger_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/app_colors.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with RestorationMixin {
  @override
  String? get restorationId => 'settings_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   ref.read(settingsNotifierProvider.notifier).loadSettings();
    // });
  }

  @override
  Widget build(BuildContext context) {
    // 닉네임 관련 코드 완전 제거
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '설정',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: ListView(
          restorationId: 'settings_list_scroll',
          cacheExtent: 500,
          children: [
            // 닉네임 ListTile 완전 삭제
            // RepaintBoundary(child: ListTile(title: Text('설정 항목1'))),
            // 요일 설정 등 나머지 항목은 그대로 유지
          _buildSettingItem(
            title: '선입금 설정',
            onTap: () => _showPrepaymentSettings(),
          ),
          // [삭제] 판매자 관리 메뉴
          // _buildSettingItem(
          //   title: '판매자 관리',
          //   onTap: () => _navigateToSellerManagement(),
          // ),
          _buildSettingItem(
            title: 'UI 열 수 설정',
            onTap: () => _showUIColumnsSettings(),
          ),
          _buildSettingItem(
            title: '구현 예정 목록',
            onTap: () => _showFutureImplementationDialog(),
          ),
          // [삭제] 선입금 상품 관리
          // _buildSettingItem(
          //   title: '선입금 상품 관리',
          //   onTap: () {
          //     Navigator.of(context).push(
          //       MaterialPageRoute(
          //         builder: (context) => const PrepaymentVirtualProductManagementScreen(),
          //       ),
          //     );
          //   },
          // ),
          // [삭제] 선입금-상품 연동 관리
          // _buildSettingItem(
          //   title: '선입금-상품 연동 관리',
          //   onTap: () {
          //     Navigator.of(context).push(
          //       MaterialPageRoute(
          //         builder: (context) => const PrepaymentProductLinkScreen(),
          //       ),
          //     );
          //   },
          // ),
          ],
        ),
      ),
    );
  }

  /// 설정 아이템 위젯
  Widget _buildSettingItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Material(
        child: InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontFamily: 'Pretendard',
                fontSize: 18, 
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 요일 설정 다이얼로그
  // Future<void> _showDayOfWeekSettings() async {
  //   await showDialog(
  //     context: context,
  //     builder: (context) => Consumer(
  //       builder: (context, ref, child) {
  //         return _DayOfWeekSettingsDialog(
  //           enabledDays: ref.watch(enabledDaysOfWeekProvider),
  //           onDaysChanged: (days) {
  //             ref.read(settingsNotifierProvider.notifier).setEnabledDaysOfWeek(days);
  //           },
  //         );
  //       },
  //     ),
  //   );
  // }

  /// 선입금 설정 다이얼로그
  Future<void> _showPrepaymentSettings() async {
    await showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          return ref.watch(settingsNotifierProvider).when(
            data: (state) => _PrepaymentSettingsDialog(
              collectDayOfWeekFromExcel: state.collectDayOfWeekFromExcel,
              excelDayOfWeekColumnIndex: state.excelDayOfWeekColumnIndex,
              linkPrepaymentToInventory: state.linkPrepaymentToInventory,
              onCollectDayOfWeekChanged: (value) async {
                LoggerUtils.logInfo('요일 수집 설정 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setCollectDayOfWeekFromExcel(value);
                LoggerUtils.logInfo('요일 수집 설정 변경 완료', tag: 'SettingsScreen');
              },
              onColumnIndexChanged: (value) async {
                LoggerUtils.logInfo('요일 열 인덱스 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setExcelDayOfWeekColumnIndex(value);
                LoggerUtils.logInfo('요일 열 인덱스 변경 완료', tag: 'SettingsScreen');
              },
              onLinkInventoryChanged: (value) async {
                LoggerUtils.logInfo('재고 연동 설정 변경: $value', tag: 'SettingsScreen');
                await ref
                    .read(settingsNotifierProvider.notifier)
                    .setLinkPrepaymentToInventory(value);
                LoggerUtils.logInfo('재고 연동 설정 변경 완료', tag: 'SettingsScreen');
              },
            ),
            loading: () => const AlertDialog(
              title: Text('선입금 설정'),
              content: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => AlertDialog(
              title: const Text('선입금 설정'),
              content: Text('설정을 불러오는 중 오류가 발생했습니다: ${error.toString()}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('확인'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// UI 열 수 설정 다이얼로그 (통합 버전)
  Future<void> _showUIColumnsSettings() async {
    await showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          return ref.watch(settingsNotifierProvider).when(
            data: (state) => _UIColumnsSettingsDialog(
              columnsPortrait: state.inventoryColumnsPortrait,
              columnsLandscape: state.inventoryColumnsLandscape,
              onColumnsPortraitChanged: (value) async {
                LoggerUtils.logInfo('UI 세로모드 열 수 변경: $value', tag: 'SettingsScreen');
                // 재고현황과 판매 화면 모두 동일한 값으로 설정
                await Future.wait([
                  ref.read(settingsNotifierProvider.notifier).setInventoryColumnsPortrait(value),
                  ref.read(settingsNotifierProvider.notifier).setSaleColumnsPortrait(value),
                ]);
                LoggerUtils.logInfo('UI 세로모드 열 수 변경 완료', tag: 'SettingsScreen');
              },
              onColumnsLandscapeChanged: (value) async {
                LoggerUtils.logInfo('UI 가로모드 열 수 변경: $value', tag: 'SettingsScreen');
                // 재고현황과 판매 화면 모두 동일한 값으로 설정
                await Future.wait([
                  ref.read(settingsNotifierProvider.notifier).setInventoryColumnsLandscape(value),
                  ref.read(settingsNotifierProvider.notifier).setSaleColumnsLandscape(value),
                ]);
                LoggerUtils.logInfo('UI 가로모드 열 수 변경 완료', tag: 'SettingsScreen');
              },
            ),
            loading: () => const AlertDialog(
              title: Text('UI 열 수 설정'),
              content: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => AlertDialog(
              title: const Text('UI 열 수 설정'),
              content: Text('설정을 불러오는 중 오류가 발생했습니다: ${error.toString()}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('확인'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 구현 예정 목록 다이얼로그
  Future<void> _showFutureImplementationDialog() async {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    await showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Padding(
          padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 아이콘과 제목을 한 줄로 배치
              Row(
                children: [
                  custom_dialog.DialogTheme.buildCompactIconContainer(
                    icon: Icons.info_outline_rounded,
                    color: OnboardingColors.primary,
                    isTablet: isTablet,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Text(
                      '구현 예정 목록',
                      style: custom_dialog.DialogTheme.titleStyle.copyWith(
                        fontSize: isTablet ? 20.0 : 18.0,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 내용
              Container(
                padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      OnboardingColors.surfaceVariant,
                      OnboardingColors.secondary.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '현재 모든 주요 기능이 구현 완료되었습니다.',
                      style: TextStyle(
                        fontSize: isTablet ? 16.0 : 14.0,
                        color: OnboardingColors.textPrimary,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                    SizedBox(height: isTablet ? 12.0 : 8.0),
                    Text(
                      '추가 기능 요청이 있으시면 개발자에게 문의해주세요.',
                      style: TextStyle(
                        fontSize: isTablet ? 16.0 : 14.0,
                        color: OnboardingColors.textSecondary,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 확인 버튼
              SizedBox(
                width: double.infinity,
                child: custom_dialog.DialogTheme.buildGradientButton(
                  decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                  onPressed: () => Navigator.of(context).pop(),
                  isCompact: true,
                  child: Text(
                    '확인',
                    style: TextStyle(
                      color: OnboardingColors.textOnPrimary,
                      fontSize: isTablet ? 16.0 : 14.0,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Pretendard',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 선입금 설정 다이얼로그
class _PrepaymentSettingsDialog extends StatefulWidget {
  final bool collectDayOfWeekFromExcel;
  final int excelDayOfWeekColumnIndex;
  final bool linkPrepaymentToInventory;
  final ValueChanged<bool> onCollectDayOfWeekChanged;
  final ValueChanged<int> onColumnIndexChanged;
  final ValueChanged<bool> onLinkInventoryChanged;

  const _PrepaymentSettingsDialog({
    required this.collectDayOfWeekFromExcel,
    required this.excelDayOfWeekColumnIndex,
    required this.linkPrepaymentToInventory,
    required this.onCollectDayOfWeekChanged,
    required this.onColumnIndexChanged,
    required this.onLinkInventoryChanged,
  });

  @override
  State<_PrepaymentSettingsDialog> createState() =>
      _PrepaymentSettingsDialogState();
}

class _PrepaymentSettingsDialogState extends State<_PrepaymentSettingsDialog> {
  late TextEditingController _dayOfWeekController;

  /// 인덱스를 엑셀 열 문자로 변환합니다 (0 -> A, 1 -> B, ...)
  String _indexToColumnLetter(int index) {
    if (index < 0) return '';
    String result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = (index ~/ 26) - 1;
    }
    return result;
  }

  @override
  void initState() {
    super.initState();
    _dayOfWeekController = TextEditingController(
      text: widget.excelDayOfWeekColumnIndex >= 0
          ? _indexToColumnLetter(widget.excelDayOfWeekColumnIndex)
          : '',
    );
  }

  @override
  void dispose() {
    _dayOfWeekController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 아이콘과 제목을 한 줄로 배치
            Row(
              children: [
                custom_dialog.DialogTheme.buildCompactIconContainer(
                  icon: Icons.account_balance_wallet_rounded,
                  color: OnboardingColors.accent,
                  isTablet: isTablet,
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                Expanded(
                  child: Text(
                    '선입금 설정',
                    style: custom_dialog.DialogTheme.titleStyle.copyWith(
                      fontSize: isTablet ? 20.0 : 18.0,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 안내 메시지 (더 컴팩트)
            Container(
              padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    OnboardingColors.primary.withValues(alpha: 0.1),
                    OnboardingColors.primaryLight.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: OnboardingColors.primary,
                    size: isTablet ? 20.0 : 16.0,
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                  Expanded(
                    child: Text(
                      '엑셀에서 요일 수집 설정은 엑셀 일괄 등록 화면 우측 상단에서 변경할 수 있습니다.',
                      style: TextStyle(
                        color: OnboardingColors.textSecondary,
                        fontSize: isTablet ? 14.0 : 12.0,
                        height: 1.3,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 재고 연동 설정 (더 컴팩트)
            Container(
              padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    OnboardingColors.surfaceVariant,
                    OnboardingColors.secondary.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8.0 : 6.0),
                    decoration: BoxDecoration(
                      gradient: widget.linkPrepaymentToInventory
                        ? LinearGradient(
                            colors: [
                              OnboardingColors.success,
                              OnboardingColors.successLight,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              OnboardingColors.secondary,
                              OnboardingColors.secondaryLight,
                            ],
                          ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.link_rounded,
                      color: widget.linkPrepaymentToInventory
                        ? OnboardingColors.textOnPrimary
                        : OnboardingColors.textSecondary,
                      size: isTablet ? 20.0 : 16.0,
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '재고 연동',
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            fontWeight: FontWeight.w600,
                            color: OnboardingColors.textPrimary,
                          ),
                        ),
                        Text(
                          '선입금 수령 시 재고 자동 차감',
                          style: TextStyle(
                            fontSize: isTablet ? 14.0 : 12.0,
                            color: OnboardingColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: widget.linkPrepaymentToInventory,
                    onChanged: (value) => widget.onLinkInventoryChanged(value),
                    activeColor: OnboardingColors.success,
                    activeTrackColor: OnboardingColors.success.withValues(alpha: 0.3),
                    inactiveThumbColor: OnboardingColors.secondary,
                    inactiveTrackColor: OnboardingColors.secondary.withValues(alpha: 0.3),
                  ),
                ],
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

            // 버튼들 (더 컴팩트)
            Row(
              children: [
                // 취소 버튼
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: OnboardingColors.secondary),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: isTablet ? 14.0 : 12.0,
                          ),
                          child: Text(
                            '취소',
                            style: TextStyle(
                              color: OnboardingColors.textSecondary,
                              fontSize: isTablet ? 16.0 : 14.0,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),

                // 확인 버튼
                Expanded(
                  child: custom_dialog.DialogTheme.buildGradientButton(
                    decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                    isCompact: true,
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      '확인',
                      style: TextStyle(
                        color: OnboardingColors.textOnPrimary,
                        fontSize: isTablet ? 16.0 : 14.0,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// UI 열 수 설정 다이얼로그 (통합 버전)
class _UIColumnsSettingsDialog extends StatefulWidget {
  final int columnsPortrait;
  final int columnsLandscape;
  final ValueChanged<int> onColumnsPortraitChanged;
  final ValueChanged<int> onColumnsLandscapeChanged;

  const _UIColumnsSettingsDialog({
    required this.columnsPortrait,
    required this.columnsLandscape,
    required this.onColumnsPortraitChanged,
    required this.onColumnsLandscapeChanged,
  });

  @override
  State<_UIColumnsSettingsDialog> createState() => _UIColumnsSettingsDialogState();
}

class _UIColumnsSettingsDialogState extends State<_UIColumnsSettingsDialog> {
  late int _columnsPortrait;
  late int _columnsLandscape;

  @override
  void initState() {
    super.initState();
    // 세로모드: 최대 8열로 제한
    _columnsPortrait = widget.columnsPortrait > 8 ? 8 : widget.columnsPortrait;
    // 가로모드: 최소 6열로 제한
    _columnsLandscape = widget.columnsLandscape < 6 ? 6 : widget.columnsLandscape;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목 중앙정렬
            Center(
              child: Text(
                'UI 열 수 설정',
                style: custom_dialog.DialogTheme.titleStyle.copyWith(
                  fontSize: isTablet ? 20.0 : 18.0,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 설정 내용
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 세로모드 설정
                    _buildModernSlider(
                      '세로모드',
                      Icons.stay_current_portrait_rounded,
                      _columnsPortrait.toDouble(),
                      (value) => setState(() => _columnsPortrait = value.round()),
                      isTablet,
                      min: 3.0,
                      max: 8.0,
                    ),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),

                    // 가로모드 설정
                    _buildModernSlider(
                      '가로모드',
                      Icons.stay_current_landscape_rounded,
                      _columnsLandscape.toDouble(),
                      (value) => setState(() => _columnsLandscape = value.round()),
                      isTablet,
                      min: 6.0,
                      max: 12.0,
                    ),

                  ],
                ),
              ),
            ),

            SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

            // 모던 버튼들
            Row(
              children: [
                // 취소 버튼 (Secondary Style)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '취소',
                    onPressed: () => Navigator.of(context).pop(),
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: 16.0),

                // 확인 버튼 (Primary Style)
                Expanded(
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '확인',
                    onPressed: () {
                      widget.onColumnsPortraitChanged(_columnsPortrait);
                      widget.onColumnsLandscapeChanged(_columnsLandscape);
                      Navigator.of(context).pop();
                    },
                    isTablet: isTablet,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 모던한 슬라이더 빌더 (한 줄에 하나씩)
  Widget _buildModernSlider(
    String label,
    IconData icon,
    double value,
    ValueChanged<double> onChanged,
    bool isTablet, {
    double min = 3.0,
    double max = 12.0,
  }) {
    return Container(
      padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            OnboardingColors.surfaceVariant,
            OnboardingColors.secondary.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: OnboardingColors.shadowLight,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 라벨과 아이콘
          Row(
            children: [
              Icon(
                icon,
                color: OnboardingColors.primary,
                size: isTablet ? 20.0 : 18.0,
              ),
              SizedBox(width: isTablet ? 12.0 : 8.0),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: isTablet ? 16.0 : 14.0,
                    fontWeight: FontWeight.w600,
                    color: OnboardingColors.textPrimary,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ),
              // 현재 값 표시
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12.0 : 8.0,
                  vertical: isTablet ? 6.0 : 4.0,
                ),
                decoration: BoxDecoration(
                  gradient: OnboardingColors.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${value.round()}열',
                  style: TextStyle(
                    fontSize: isTablet ? 14.0 : 12.0,
                    fontWeight: FontWeight.bold,
                    color: OnboardingColors.textOnPrimary,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 12.0 : 8.0),
          // 슬라이더
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: OnboardingColors.primary,
              inactiveTrackColor: OnboardingColors.secondary.withValues(alpha: 0.3),
              thumbColor: OnboardingColors.primary,
              overlayColor: OnboardingColors.primary.withValues(alpha: 0.1),
              trackHeight: isTablet ? 4.0 : 3.0,
              thumbShape: RoundSliderThumbShape(
                enabledThumbRadius: isTablet ? 12.0 : 10.0,
              ),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: (max - min).round(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
