import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/link_service.dart';
import '../services/database_service.dart';
import '../utils/toast_utils.dart';
import '../utils/logger_utils.dart';
import '../widgets/registration_complete_page.dart';
import '../screens/inventory/inventory_screen.dart';
import '../providers/prepayment_product_link_provider.dart';
import '../providers/prepayment_provider.dart';
import '../providers/product_provider.dart';
import '../providers/prepayment_virtual_product_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/excel_processor.dart';
import '../utils/event_workspace_utils.dart';

class RegistrationLinkPromptPage extends ConsumerStatefulWidget {
  final List<ExcelPrepaymentData> prepaymentData;
  const RegistrationLinkPromptPage({super.key, required this.prepaymentData});

  @override
  ConsumerState<RegistrationLinkPromptPage> createState() => _RegistrationLinkPromptPageState();
}

class _RegistrationLinkPromptPageState extends ConsumerState<RegistrationLinkPromptPage> {
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    // 페이지 진입 시 항상 최신 데이터로 강제 갱신
    Future.microtask(() async {
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      await ref.read(productNotifierProvider.notifier).loadProducts();
      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
    });
  }

  Future<void> _runLinkAndNavigate(BuildContext context) async {
    setState(() { _isProcessing = true; });
    String? errorMsg;
    int linkedCount = 0;
    try {
      final databaseService = ref.read(databaseServiceProvider);
      final linkService = LinkService(databaseService: databaseService);
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
      if (currentEvent == null) {
        throw Exception('현재 행사가 선택되지 않았습니다');
      }

      linkedCount = await linkService.linkProductsWithPrepayments(
        prepaymentDataList: widget.prepaymentData,
        context: context,
        eventId: currentEvent.id!,
      );
      await ref.read(productNotifierProvider.notifier).loadProducts();
      await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      LoggerUtils.logInfo('[RegistrationLinkPromptPage] 연동 성공, linkedCount=$linkedCount', tag: 'RegistrationLinkPromptPage');
      // ToastUtils.showSuccess(context, '[RegistrationLinkPromptPage] 연동 성공, linkedCount=$linkedCount'); // 정상 연동 시 토스트 제거
    } catch (e) {
      errorMsg = e.toString();
      LoggerUtils.logError('[RegistrationLinkPromptPage] 연동 중 오류', error: e, tag: 'RegistrationLinkPromptPage');
      ToastUtils.showError(context, '[RegistrationLinkPromptPage] 연동 중 오류: $e');
    } finally {
      setState(() { _isProcessing = false; });
    }
    if (!mounted) return;
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RegistrationCompletePage(
          description: errorMsg == null
              ? '$linkedCount개의 연동 데이터가 생성되었습니다.'
              : '연동 중 오류가 발생했습니다: $errorMsg',
          onConfirm: () {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const InventoryScreen()),
              (route) => false,
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: OnboardingColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Center(
                child: Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: isTablet ? 48.0 : 24.0,
                    vertical: isTablet ? 32.0 : 20.0,
                  ),
                  padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        OnboardingColors.surface,
                        OnboardingColors.surfaceVariant,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: OnboardingColors.shadow,
                        blurRadius: 24,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 연동 아이콘 (더 컴팩트)
                      Container(
                        padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              OnboardingColors.accent,
                              OnboardingColors.accentLight,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(40),
                          boxShadow: [
                            BoxShadow(
                              color: OnboardingColors.accent.withValues(alpha: 0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.link_rounded,
                          color: OnboardingColors.textOnPrimary,
                          size: isTablet ? 80.0 : 64.0,
                        ),
                      ),
                      SizedBox(height: isTablet ? 24.0 : 20.0),

                      // 제목
                      Text(
                        '선입금과 상품의 등록이 완료되었습니다.',
                        style: TextStyle(
                          fontSize: isTablet ? 22.0 : 18.0,
                          fontWeight: FontWeight.bold,
                          color: OnboardingColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: isTablet ? 16.0 : 12.0),

                      // 설명 (더 컴팩트)
                      Container(
                        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              OnboardingColors.primary.withValues(alpha: 0.1),
                              OnboardingColors.primaryLight.withValues(alpha: 0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: OnboardingColors.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline_rounded,
                              color: OnboardingColors.primary,
                              size: isTablet ? 20.0 : 16.0,
                            ),
                            SizedBox(width: isTablet ? 12.0 : 8.0),
                            Expanded(
                              child: Text(
                                '두 데이터를 연동하시겠습니까?\n(연동하지 않으면 나중에 수동으로도 가능합니다)',
                                style: TextStyle(
                                  fontSize: isTablet ? 15.0 : 13.0,
                                  color: OnboardingColors.textSecondary,
                                  height: 1.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 버튼들
                      Row(
                        children: [
                          // 연동하지 않기 버튼
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: OnboardingColors.secondary),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Material(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(16),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(16),
                                  onTap: _isProcessing
                                      ? null
                                      : () {
                                          Navigator.of(context).pushAndRemoveUntil(
                                            MaterialPageRoute(builder: (context) => const InventoryScreen()),
                                            (route) => false,
                                          );
                                        },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: isTablet ? 20.0 : 18.0,
                                      horizontal: isTablet ? 24.0 : 16.0,
                                    ),
                                    child: Text(
                                      '연동하지 않기',
                                      style: TextStyle(
                                        fontSize: isTablet ? 18.0 : 16.0,
                                        fontWeight: FontWeight.bold,
                                        color: OnboardingColors.textSecondary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: isTablet ? 20.0 : 16.0),

                          // 연동하기 버튼
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: OnboardingColors.primaryGradient,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: OnboardingColors.primary.withValues(alpha: 0.4),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(16),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(16),
                                  onTap: _isProcessing ? null : () => _runLinkAndNavigate(context),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: isTablet ? 20.0 : 18.0,
                                      horizontal: isTablet ? 24.0 : 16.0,
                                    ),
                                    child: _isProcessing
                                        ? SizedBox(
                                            width: isTablet ? 28.0 : 24.0,
                                            height: isTablet ? 28.0 : 24.0,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                              valueColor: AlwaysStoppedAnimation<Color>(OnboardingColors.textOnPrimary),
                                            ),
                                          )
                                        : Text(
                                            '연동하기',
                                            style: TextStyle(
                                              fontSize: isTablet ? 18.0 : 16.0,
                                              fontWeight: FontWeight.bold,
                                              color: OnboardingColors.textOnPrimary,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // 닫기 버튼
              Positioned(
                top: isTablet ? 16.0 : 8.0,
                right: isTablet ? 16.0 : 8.0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        OnboardingColors.surface,
                        OnboardingColors.surfaceVariant,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: OnboardingColors.shadowLight,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: _isProcessing
                          ? null
                          : () {
                              Navigator.of(context).pushAndRemoveUntil(
                                MaterialPageRoute(builder: (context) => const InventoryScreen()),
                                (route) => false,
                              );
                            },
                      child: Container(
                        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
                        child: Icon(
                          Icons.close_rounded,
                          color: OnboardingColors.textSecondary,
                          size: isTablet ? 32.0 : 24.0,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 