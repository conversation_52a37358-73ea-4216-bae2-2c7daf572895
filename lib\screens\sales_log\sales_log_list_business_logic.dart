import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../providers/sales_log_provider.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/confirmation_dialog.dart';
import 'sales_log_list_filter_logic.dart';
import 'sales_log_list_ui_components.dart';

/// 판매 기록 목록의 비즈니스 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 판매 기록 삭제 (재고 복구 포함)
/// - 그룹 판매 기록 삭제
/// - 그룹 상세 다이얼로그 관리
/// - 필터링된 데이터 관리
class SalesLogListBusinessLogic {
  /// 필터링된 표시 아이템 목록 생성
  ///
  /// [ref]: Riverpod ref
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터링된 표시 아이템 목록
  static List<SalesLogDisplayItem> getFilteredDisplayItems({
    required WidgetRef ref,
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
    required DateTimeRange? selectedDateRange,
  }) {
    final allDisplayItems = ref.watch(salesLogDisplayItemsProvider);
    
    return SalesLogListFilterLogic.getFilteredDisplayItems(
      allDisplayItems: allDisplayItems,
      selectedSeller: selectedSeller,
      selectedTransactionType: selectedTransactionType,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [salesLog]: 삭제할 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteSalesLogWithStockRestore({
    required WidgetRef ref,
    required BuildContext context,
    required SalesLog salesLog,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteConfirmDialog(
        context,
        salesLog,
      );

      if (!shouldDelete) {
        return false;
      }

      // 판매 기록 삭제 및 재고 복구 (SalesLogCrud에서 처리)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteSingleSaleAndUpdateStock(salesLog);
    
      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [groupedSale]: 삭제할 그룹 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteGroupSalesLogWithStockRestore({
    required WidgetRef ref,
    required BuildContext context,
    required GroupedSale groupedSale,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteGroupConfirmDialog(
        context,
        groupedSale,
      );

      if (!shouldDelete) {
        return false;
      }

      // 그룹 판매 기록 삭제 및 재고 복구 (SalesLogCrud에서 처리)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteGroupedSaleAndUpdateStock(groupedSale);

      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '그룹 삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 상세 다이얼로그 표시
  ///
  /// [context]: BuildContext
  /// [groupedSale]: 표시할 그룹 판매 기록
  /// [selectedSeller]: 선택된 판매자 (필터링용)
  /// [onItemDelete]: 개별 아이템 삭제 콜백
  /// [productCategoryMap]: 상품ID -> 카테고리명 매핑 (선택사항)
  static void showGroupDetailDialog({
    required BuildContext context,
    required GroupedSale groupedSale,
    required String selectedSeller,
    required Function(SalesLog) onItemDelete,
    Map<int, String>? productCategoryMap,
  }) {
    // 모든 상품을 표시하되, 판매자 필터가 적용된 경우 해당 판매자 상품을 위로 정렬
    List<SalesLog> itemsToShow = List<SalesLog>.from(groupedSale.items);

    // 판매자 필터가 적용된 경우 해당 판매자 상품을 위로 정렬
    if (selectedSeller != '전체 판매자') {
      itemsToShow.sort((a, b) {
        final aIsCurrentSeller =
            (a.sellerName ?? '알 수 없음') == selectedSeller;
        final bIsCurrentSeller =
            (b.sellerName ?? '알 수 없음') == selectedSeller;

        // 해당 판매자 상품을 우선으로 정렬
        if (aIsCurrentSeller && !bIsCurrentSeller) return -1;
        if (!aIsCurrentSeller && bIsCurrentSeller) return 1;

        // 같은 우선순위 내에서는 상품명 순으로 정렬
        return a.productName.compareTo(b.productName);
      });
    }

    final currentGroupedItems = List<SalesLog>.from(itemsToShow);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // 현재 표시 중인 아이템들의 총계 재계산
            int currentTotalQuantity = currentGroupedItems.fold<int>(
              0,
              (sum, item) => sum + item.soldQuantity,
            );
            int currentTotalAmount = currentGroupedItems.fold<int>(
              0,
              (sum, item) => sum + item.totalAmount,
            );

            // 세트 할인 정보 계산
            int totalSetDiscountAmount = currentGroupedItems.fold<int>(
              0,
              (sum, item) => sum + item.setDiscountAmount,
            );

            String dialogTitle =
                '묶음 판매 상세 (총 $currentTotalQuantity개, ${_formatCurrency(currentTotalAmount)})';

            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.0),
              ),
              backgroundColor: Colors.white,
              contentPadding: const EdgeInsets.all(24.0),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(dialogTitle),
                  // 세트 할인 정보 (있는 경우에만 표시)
                  if (totalSetDiscountAmount > 0) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.local_offer,
                            size: 16,
                            color: Colors.green.shade600,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '세트 할인 적용: -${_formatCurrency(totalSetDiscountAmount)}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              content: SizedBox(
                width: double.maxFinite,
                height: 300,
                child: ListView.builder(
                  itemCount: currentGroupedItems.length,
                  itemBuilder: (context, index) {
                    final item = currentGroupedItems[index];
                    // 현재 판매자 필터에 해당하는지 확인
                    final isCurrentSeller =
                        selectedSeller == '전체 판매자' ||
                        (item.sellerName ?? '알 수 없음') == selectedSeller;

                    return Opacity(
                      opacity: isCurrentSeller ? 1.0 : 0.5, // 다른 판매자는 흐릿하게
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 2),
                        decoration: BoxDecoration(
                          border: isCurrentSeller
                              ? null
                              : Border.all(
                                  color: Colors.grey.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListTile(
                          title: Text(
                            _buildProductDisplayName(item, productCategoryMap),
                            style: TextStyle(
                              color: isCurrentSeller
                                  ? Colors.black87
                                  : Colors.grey,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${item.transactionType.displayName} • ${item.sellerName ?? '알 수 없음'}',
                                style: TextStyle(
                                  color: isCurrentSeller
                                      ? Colors.black54
                                      : Colors.grey,
                                ),
                              ),
                              // 세트 할인 정보 (있는 경우에만 표시)
                              if (item.setDiscountAmount > 0 && item.setDiscountNames != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 2),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.local_offer,
                                        size: 12,
                                        color: isCurrentSeller
                                            ? Colors.green.shade600
                                            : Colors.grey,
                                      ),
                                      const SizedBox(width: 2),
                                      Expanded(
                                        child: Text(
                                          '세트 할인: ${item.setDiscountNames} (-${_formatCurrency(item.setDiscountAmount)})',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: isCurrentSeller
                                                ? Colors.green.shade700
                                                : Colors.grey,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '${item.soldQuantity}개 • ${_formatCurrency(item.totalAmount)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: isCurrentSeller
                                      ? Colors.black87
                                      : Colors.grey,
                                ),
                              ),
                              const SizedBox(width: 8),
                              // 개별 삭제 버튼
                              SizedBox(
                                width: 32,
                                height: 32,
                                child: IconButton(
                                  onPressed: () async {
                                    // 개별 삭제 확인 다이얼로그
                                    final shouldDelete = await ConfirmationDialog.showDelete(
                                      context: context,
                                      title: '판매 기록 삭제',
                                      message: '\'${item.productName}\' 판매 기록을 삭제하시겠습니까? 해당 상품의 재고가 ${item.soldQuantity}만큼 복원됩니다.',
                                      confirmLabel: '삭제',
                                      cancelLabel: '취소',
                                    );

                                    if (shouldDelete == true) {
                                      // 개별 삭제 실행
                                      await onItemDelete(item);

                                      // 현재 그룹에서 삭제된 아이템 제거
                                      currentGroupedItems.removeAt(index);

                                      if (currentGroupedItems.isEmpty) {
                                        // 그룹이 비어있으면 다이얼로그 닫기
                                        if (context.mounted) {
                                          Navigator.of(context).pop();
                                        }
                                      } else {
                                        // 그룹이 남아있으면 UI 업데이트
                                        setState(() {});
                                      }

                                      // 토스트 메시지 표시
                                      if (context.mounted) {
                                        ToastUtils.showToast(
                                          context,
                                          '판매 기록이 삭제되었습니다.',
                                          duration: ToastUtils.shortDuration,
                                        );
                                      }
                                    }
                                  },
                                  icon: Icon(
                                    Icons.delete_outline,
                                    size: 16,
                                    color: isCurrentSeller
                                        ? Colors.red
                                        : Colors.grey,
                                  ),
                                  padding: EdgeInsets.zero,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('닫기'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 통화 포맷팅 헬퍼 메서드
  static String _formatCurrency(int amount) {
    return '₩${amount.toStringAsFixed(0)}';
  }

  /// 상품명에 카테고리명을 포함하여 표시명 생성
  static String _buildProductDisplayName(SalesLog item, Map<int, String>? productCategoryMap) {
    // 카테고리 정보가 있고 productId가 있는 경우 카테고리명 포함
    if (productCategoryMap != null && item.productId != null) {
      final categoryName = productCategoryMap[item.productId];
      if (categoryName != null) {
        return '[$categoryName]${item.productName}';
      }
    }

    // 기본적으로 상품명만 반환
    return item.productName;
  }
}