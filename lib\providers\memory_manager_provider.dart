import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/memory_manager.dart';

/// 전역 MemoryManager Provider
final memoryManagerProvider = Provider<MemoryManager>((ref) {
  final memoryManager = MemoryManager(
    config: MemoryManagerConfig.highPerformance(),
  );

  // 메모리 압박 수준 변경 시 자동 최적화
  memoryManager.onPressureLevelChanged = (pressureLevel) {
    switch (pressureLevel) {
      case MemoryPressureLevel.critical:
        // 긴급 상황: 모든 캐시 정리 및 강제 GC
        memoryManager.performOptimization(MemoryOptimizationStrategy.aggressive);
        break;
      case MemoryPressureLevel.high:
        // 높은 압박: 적응형 최적화
        memoryManager.performOptimization(MemoryOptimizationStrategy.adaptive);
        break;
      case MemoryPressureLevel.medium:
        // 중간 압박: 보수적 최적화
        memoryManager.performOptimization(MemoryOptimizationStrategy.conservative);
        break;
      case MemoryPressureLevel.low:
        // 낮은 압박: 최적화 없음
        break;
    }
  };

  // Provider dispose 시 리소스 정리
  ref.onDispose(() {
    memoryManager.onPressureLevelChanged = null;
    memoryManager.onMemoryUsageChanged = null;
    memoryManager.onOptimizationStrategyChanged = null;
    memoryManager.onMemoryLeakDetected = null;
    memoryManager.dispose();
  });
  
  return memoryManager;
});

/// 메모리 사용량 Provider
final memoryUsageProvider = StreamProvider<MemoryUsage>((ref) async* {
  final memoryManager = ref.read(memoryManagerProvider);

  // 초기화
  await memoryManager.initialize();

  // StreamController를 사용하여 안전한 스트림 생성
  late StreamController<MemoryUsage> controller;
  Timer? timer;

  controller = StreamController<MemoryUsage>(
    onListen: () {
      // 타이머 시작
      timer = Timer.periodic(const Duration(seconds: 30), (t) async {
        try {
          final usage = await memoryManager.checkMemoryUsage();
          if (!controller.isClosed) {
            controller.add(usage);
          }
        } catch (e) {
          if (!controller.isClosed) {
            controller.addError(e);
          }
        }
      });
    },
    onCancel: () {
      // 리소스 정리
      timer?.cancel();
      timer = null;
    },
  );

  // Provider가 dispose될 때 정리
  ref.onDispose(() {
    timer?.cancel();
    controller.close();
  });

  // 초기 값 제공
  try {
    final initialUsage = await memoryManager.checkMemoryUsage();
    yield initialUsage;
  } catch (e) {
    // 초기 값 로드 실패 시 기본값 제공
    yield MemoryUsage(
      usedMemory: 0,
      totalMemory: 1,
      availableMemory: 1,
      usagePercentage: 0.0,
    );
  }

  // 스트림 연결
  yield* controller.stream;
});

/// 메모리 압박 수준 Provider
final memoryPressureLevelProvider = Provider<MemoryPressureLevel>((ref) {
  final memoryUsage = ref.watch(memoryUsageProvider);
  return memoryUsage.when(
    data: (usage) => usage.pressureLevel,
    loading: () => MemoryPressureLevel.low,
    error: (_, __) => MemoryPressureLevel.low,
  );
});

/// 메모리 최적화 전략 Provider
final memoryOptimizationStrategyProvider = Provider<MemoryOptimizationStrategy>((ref) {
  final pressureLevel = ref.watch(memoryPressureLevelProvider);
  
  switch (pressureLevel) {
    case MemoryPressureLevel.critical:
      return MemoryOptimizationStrategy.aggressive;
    case MemoryPressureLevel.high:
      return MemoryOptimizationStrategy.adaptive;
    case MemoryPressureLevel.medium:
      return MemoryOptimizationStrategy.conservative;
    case MemoryPressureLevel.low:
      return MemoryOptimizationStrategy.none;
  }
});

/// 메모리 통계 Provider
final memoryStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final memoryManager = ref.read(memoryManagerProvider);
  return memoryManager.getStats();
});

/// 메모리 캐시 Provider
final memoryCacheProvider = Provider<Map<String, dynamic>>((ref) {
  final memoryManager = ref.read(memoryManagerProvider);
  // 캐시는 private이므로 통계를 통해 간접적으로 접근
  final stats = memoryManager.getStats();
  return {'cacheSize': stats['cacheSize']};
});

/// 메모리 히스토리 Provider
final memoryHistoryProvider = Provider<List<MemoryUsage>>((ref) {
  final memoryManager = ref.read(memoryManagerProvider);
  return memoryManager.getMemoryHistory();
}); 