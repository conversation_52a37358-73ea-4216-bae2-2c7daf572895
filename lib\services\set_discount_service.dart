import '../models/set_discount.dart';
import '../utils/logger_utils.dart';

/// 세트 할인 적용 결과를 나타내는 클래스
class SetDiscountResult {
  final List<AppliedSetDiscount> appliedDiscounts;
  final int totalDiscountAmount;
  final Map<int, int> usedProductQuantities; // productId -> 사용된 수량

  const SetDiscountResult({
    required this.appliedDiscounts,
    required this.totalDiscountAmount,
    required this.usedProductQuantities,
  });

  SetDiscountResult.empty()
      : appliedDiscounts = [],
        totalDiscountAmount = 0,
        usedProductQuantities = {};
}

/// 적용된 세트 할인 정보
class AppliedSetDiscount {
  final SetDiscount setDiscount;
  final int appliedCount; // 몇 세트가 적용되었는지

  const AppliedSetDiscount({
    required this.setDiscount,
    required this.appliedCount,
  });

  int get totalDiscountAmount => setDiscount.discountAmount * appliedCount;
}

/// 세트 할인 비즈니스 로직을 처리하는 서비스 클래스
class SetDiscountService {
  /// 선택된 상품들에 대해 최적의 세트 할인을 계산합니다.
  /// 
  /// [selectedProducts]: 선택된 상품 ID와 수량의 맵 (productId -> quantity)
  /// [availableSetDiscounts]: 사용 가능한 세트 할인 목록
  /// 
  /// 반환값: 최적의 세트 할인 적용 결과
  static SetDiscountResult calculateOptimalDiscount(
    Map<int, int> selectedProducts,
    List<SetDiscount> availableSetDiscounts,
  ) {
    try {
      if (selectedProducts.isEmpty || availableSetDiscounts.isEmpty) {
        return SetDiscountResult.empty();
      }

      LoggerUtils.logDebug(
        'Calculating optimal discount for products: $selectedProducts',
        tag: 'SetDiscountService',
      );

      // 적용 가능한 세트 할인들을 필터링
      final applicableDiscounts = availableSetDiscounts
          .where((discount) => _canApplySetDiscount(selectedProducts, discount))
          .toList();

      if (applicableDiscounts.isEmpty) {
        LoggerUtils.logDebug('No applicable set discounts found', tag: 'SetDiscountService');
        return SetDiscountResult.empty();
      }

      // 할인 금액 기준으로 내림차순 정렬 (그리디 알고리즘)
      applicableDiscounts.sort((a, b) => b.discountAmount.compareTo(a.discountAmount));

      // 최적 조합 계산
      final result = _findOptimalCombination(selectedProducts, applicableDiscounts);

      LoggerUtils.logInfo(
        'Optimal discount calculated: ${result.totalDiscountAmount}원, ${result.appliedDiscounts.length} sets applied',
        tag: 'SetDiscountService',
      );

      return result;
    } catch (e) {
      LoggerUtils.logError('Failed to calculate optimal discount', error: e, tag: 'SetDiscountService');
      return SetDiscountResult.empty();
    }
  }

  /// 세트 할인이 적용 가능한지 확인
  static bool _canApplySetDiscount(Map<int, int> selectedProducts, SetDiscount setDiscount) {
    for (final productId in setDiscount.productIds) {
      if (!selectedProducts.containsKey(productId) || selectedProducts[productId]! <= 0) {
        return false;
      }
    }
    return true;
  }

  /// 최적의 세트 할인 조합을 찾습니다 (그리디 알고리즘)
  static SetDiscountResult _findOptimalCombination(
    Map<int, int> selectedProducts,
    List<SetDiscount> applicableDiscounts,
  ) {
    final appliedDiscounts = <AppliedSetDiscount>[];
    final remainingProducts = Map<int, int>.from(selectedProducts);
    int totalDiscountAmount = 0;

    // 각 세트 할인에 대해 최대한 많이 적용
    for (final setDiscount in applicableDiscounts) {
      final maxApplicableCount = _getMaxApplicableCount(remainingProducts, setDiscount);
      
      if (maxApplicableCount > 0) {
        // 세트 할인 적용
        appliedDiscounts.add(AppliedSetDiscount(
          setDiscount: setDiscount,
          appliedCount: maxApplicableCount,
        ));

        // 사용된 상품 수량 차감
        for (final productId in setDiscount.productIds) {
          remainingProducts[productId] = remainingProducts[productId]! - maxApplicableCount;
        }

        totalDiscountAmount += setDiscount.discountAmount * maxApplicableCount;

        LoggerUtils.logDebug(
          'Applied set discount: ${setDiscount.name} x$maxApplicableCount (${setDiscount.discountAmount * maxApplicableCount}원)',
          tag: 'SetDiscountService',
        );
      }
    }

    // 사용된 상품 수량 계산
    final usedProductQuantities = <int, int>{};
    for (final productId in selectedProducts.keys) {
      final used = selectedProducts[productId]! - (remainingProducts[productId] ?? 0);
      if (used > 0) {
        usedProductQuantities[productId] = used;
      }
    }

    return SetDiscountResult(
      appliedDiscounts: appliedDiscounts,
      totalDiscountAmount: totalDiscountAmount,
      usedProductQuantities: usedProductQuantities,
    );
  }

  /// 특정 세트 할인을 최대 몇 번 적용할 수 있는지 계산
  static int _getMaxApplicableCount(Map<int, int> remainingProducts, SetDiscount setDiscount) {
    int maxCount = 999999; // 충분히 큰 수

    for (final productId in setDiscount.productIds) {
      final availableQuantity = remainingProducts[productId] ?? 0;
      if (availableQuantity <= 0) {
        return 0; // 하나라도 없으면 적용 불가
      }
      maxCount = maxCount < availableQuantity ? maxCount : availableQuantity;
    }

    return maxCount == 999999 ? 0 : maxCount;
  }

  /// 세트 할인 적용 결과를 사용자에게 보여줄 문자열로 변환
  static String formatDiscountSummary(SetDiscountResult result) {
    if (result.appliedDiscounts.isEmpty) {
      return '';
    }

    final buffer = StringBuffer();
    buffer.writeln('🎁 세트 할인 적용:');
    
    for (final applied in result.appliedDiscounts) {
      if (applied.appliedCount == 1) {
        buffer.writeln('• ${applied.setDiscount.name}: -${_formatCurrency(applied.setDiscount.discountAmount)}');
      } else {
        buffer.writeln('• ${applied.setDiscount.name} x${applied.appliedCount}: -${_formatCurrency(applied.totalDiscountAmount)}');
      }
    }
    
    buffer.writeln('총 할인: -${_formatCurrency(result.totalDiscountAmount)}');
    
    return buffer.toString().trim();
  }

  /// 간단한 통화 포맷팅
  static String _formatCurrency(int amount) {
    return '${amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}원';
  }

  /// 세트 할인이 적용된 상품들의 목록을 반환
  static List<int> getDiscountedProductIds(SetDiscountResult result) {
    final productIds = <int>{};
    for (final applied in result.appliedDiscounts) {
      productIds.addAll(applied.setDiscount.productIds);
    }
    return productIds.toList();
  }

  /// 특정 상품이 세트 할인에 포함되는지 확인
  static bool isProductInDiscount(int productId, SetDiscountResult result) {
    return result.usedProductQuantities.containsKey(productId) &&
           result.usedProductQuantities[productId]! > 0;
  }

  /// 세트 할인 검증 (상품 ID들이 유효한지 확인)
  static bool validateSetDiscount(SetDiscount setDiscount, List<int> availableProductIds) {
    if (setDiscount.productIds.isEmpty) {
      return false;
    }

    // 중복 상품 ID 확인
    final uniqueIds = setDiscount.productIds.toSet();
    if (uniqueIds.length != setDiscount.productIds.length) {
      return false;
    }

    // 모든 상품 ID가 유효한지 확인
    for (final productId in setDiscount.productIds) {
      if (!availableProductIds.contains(productId)) {
        return false;
      }
    }

    return true;
  }
}
