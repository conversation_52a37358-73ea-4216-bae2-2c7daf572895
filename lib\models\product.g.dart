// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Product _$ProductFromJson(Map<String, dynamic> json) => _Product(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  price: (json['price'] as num).toInt(),
  quantity: (json['quantity'] as num).toInt(),
  sellerName: json['sellerName'] as String?,
  imagePath: json['imagePath'] as String?,
  focusX: (json['focusX'] as num?)?.toDouble(),
  focusY: (json['focusY'] as num?)?.toDouble(),
  isActive: json['isActive'] as bool? ?? true,
  lastServicedDate: (json['lastServicedDate'] as num?)?.toInt(),
  eventId: (json['eventId'] as num?)?.toInt() ?? 1,
  categoryId: (json['categoryId'] as num?)?.toInt() ?? 1,
  syncMetadata: json['syncMetadata'] == null
      ? null
      : SyncMetadata.fromJson(json['syncMetadata'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ProductToJson(_Product instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'price': instance.price,
  'quantity': instance.quantity,
  'sellerName': instance.sellerName,
  'imagePath': instance.imagePath,
  'focusX': instance.focusX,
  'focusY': instance.focusY,
  'isActive': instance.isActive,
  'lastServicedDate': instance.lastServicedDate,
  'eventId': instance.eventId,
  'categoryId': instance.categoryId,
  'syncMetadata': instance.syncMetadata,
};
