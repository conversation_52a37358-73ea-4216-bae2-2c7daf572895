import 'package:flutter_test/flutter_test.dart';
import 'package:parabara/models/seller.dart';
import 'package:parabara/repositories/seller_repository.dart';
import 'package:parabara/services/database_service.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late Database db;
  late DatabaseService databaseService;
  late SellerRepository repository;

  setUpAll(() {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    db = await databaseFactory.openDatabase(
      inMemoryDatabasePath,
      options: OpenDatabaseOptions(
        version: 23,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE sellers (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              isDefault INTEGER DEFAULT 0
            )
          ''');
        },
        onUpgrade: (db, oldVersion, newVersion) async {
          if (oldVersion < 23) {
            // sellers 테이블에 isDefault 컬럼 추가
            await db.execute('''
              ALTER TABLE sellers ADD COLUMN isDefault INTEGER DEFAULT 0
            ''');
          }
        },
      ),
    );

    databaseService = TestDatabaseService(db);
    repository = SellerRepository(database: databaseService);

    // 테스트 데이터 삽입
    await db.insert('sellers', {
      'name': 'Test Seller',
      'isDefault': 0,
    });
  });

  tearDown(() async {
    await db.close();
  });

  group('SellerRepository Tests', () {
    test('getAllSellers should return all sellers', () async {
      final sellers = await repository.getAllSellers();
      expect(sellers, isNotEmpty);
      expect(sellers.first.name, equals('Test Seller'));
    });

    test('insertSeller should add new seller', () async {
      final seller = Seller(name: 'New Seller');

      await repository.insertSeller(seller);
      final sellers = await repository.getAllSellers();
      expect(sellers.length, greaterThan(1));
    });

    test('getDefaultSeller should return default seller', () async {
      // 대표 판매자 설정
      await db.update('sellers', {'isDefault': 1}, where: 'name = ?', whereArgs: ['Test Seller']);
      
      final defaultSeller = await repository.getDefaultSeller();
      expect(defaultSeller, isNotNull);
      expect(defaultSeller!.name, equals('Test Seller'));
      expect(defaultSeller.isDefault, isTrue);
    });

    test('setDefaultSeller should set seller as default', () async {
      // 새 판매자 추가
      final newSeller = Seller(name: 'New Default Seller');
      final newSellerId = await repository.insertSeller(newSeller);
      
      // 대표 판매자로 설정
      await repository.setDefaultSeller(newSellerId);
      
      final defaultSeller = await repository.getDefaultSeller();
      expect(defaultSeller, isNotNull);
      expect(defaultSeller!.name, equals('New Default Seller'));
      expect(defaultSeller.isDefault, isTrue);
      
      // 기존 판매자의 대표 설정이 해제되었는지 확인
      final allSellers = await repository.getAllSellers();
      final testSeller = allSellers.firstWhere((s) => s.name == 'Test Seller');
      expect(testSeller.isDefault, isFalse);
    });

    test('unsetDefaultSeller should unset default seller', () async {
      // 대표 판매자 설정
      await db.update('sellers', {'isDefault': 1}, where: 'name = ?', whereArgs: ['Test Seller']);
      
      // 대표 판매자 해제
      final testSeller = await repository.getSellerByName('Test Seller');
      await repository.unsetDefaultSeller(testSeller!.id!);
      
      final defaultSeller = await repository.getDefaultSeller();
      expect(defaultSeller, isNull);
      
      // 판매자의 대표 설정이 해제되었는지 확인
      final updatedSeller = await repository.getSellerByName('Test Seller');
      expect(updatedSeller!.isDefault, isFalse);
    });

    test('isDefaultSeller should check if seller is default', () async {
      // 대표 판매자 설정
      await db.update('sellers', {'isDefault': 1}, where: 'name = ?', whereArgs: ['Test Seller']);
      
      final testSeller = await repository.getSellerByName('Test Seller');
      final isDefault = await repository.isDefaultSeller(testSeller!.id!);
      expect(isDefault, isTrue);
      
      // 대표 판매자 해제
      await repository.unsetDefaultSeller(testSeller.id!);
      final isDefaultAfterUnset = await repository.isDefaultSeller(testSeller.id!);
      expect(isDefaultAfterUnset, isFalse);
    });
  });
}

/// 테스트용 DatabaseService 구현
class TestDatabaseService implements DatabaseService {
  final Database _db;

  TestDatabaseService(this._db);

  @override
  Future<Database> get database async => _db;

  @override
  Future<void> forceMigration() async {
    // 테스트에서는 forceMigration이 필요하지 않으므로 빈 구현
  }
}
