import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/service_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/currency_utils.dart';

import '../../widgets/confirmation_dialog.dart';
import '../../utils/toast_utils.dart';

/// 서비스 처리 화면
/// 등록된 상품들을 표시하고 수량을 입력하여 서비스 처리할 수 있는 화면
class ServiceScreen extends ConsumerStatefulWidget {
  const ServiceScreen({super.key});

  @override
  ConsumerState<ServiceScreen> createState() => _ServiceScreenState();
}

class _ServiceScreenState extends ConsumerState<ServiceScreen> {
  static const String _tag = 'ServiceScreen';
  
  // 각 상품별 서비스 수량을 저장하는 맵
  final Map<int, int> _serviceQuantities = {};
  
  // 텍스트 컨트롤러들을 저장하는 맵
  final Map<int, TextEditingController> _controllers = {};

  @override
  void dispose() {
    // 모든 텍스트 컨트롤러 해제
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LoggerUtils.methodStart('build', tag: _tag);

    return Scaffold(
      appBar: AppBar(
        title: const Text('서비스 처리'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: '도움말',
          ),
        ],
      ),
      body: SafeArea(
        child: Consumer(
          builder: (context, ref, child) {
          final productState = ref.watch(productNotifierProvider);
          
          if (productState.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (productState.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '상품 목록을 불러오는 중 오류가 발생했습니다.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    productState.errorMessage!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.red[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(productNotifierProvider.notifier).loadProducts();
                    },
                    child: const Text('다시 시도'),
                  ),
                ],
              ),
            );
          }

          final activeProducts = productState.products
              .where((product) => product.isActive && product.quantity > 0)
              .toList();

          if (activeProducts.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '서비스 처리할 수 있는 상품이 없습니다.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '재고가 있는 활성 상품만 서비스 처리할 수 있습니다.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 안내 메시지
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          '서비스 처리 안내',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• 서비스 처리 시 재고는 차감되지만 판매 금액은 0원으로 처리됩니다.\n'
                      '• 판매 기록에 "서비스"로 표시됩니다.\n'
                      '• 재고가 있는 상품만 서비스 처리할 수 있습니다.',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 상품 목록
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: activeProducts.length,
                  itemExtent: 90.0, // 고정 높이로 스크롤 성능 최적화 (Card + padding + content)
                  itemBuilder: (context, index) {
                    final product = activeProducts[index];
                    return _buildProductItem(product);
                  },
                ),
              ),
              
              // 서비스 처리 버튼
              SafeArea(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                  child: ElevatedButton(
                    onPressed: _hasSelectedProducts() ? _processService : null,
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                      backgroundColor: Colors.orange[600],
                      foregroundColor: Colors.white,
                    ),
                    child: Text(
                      '서비스 처리 (${_getTotalSelectedQuantity()}개)',
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                        fontFamily: 'Pretendard',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        ),
      ),
    );
  }

  /// 상품 아이템 위젯 빌드
  Widget _buildProductItem(Product product) {
    // product.id가 null인 경우 처리
    if (product.id == null) {
      return const SizedBox.shrink();
    }

    final productId = product.id!;

    // 컨트롤러가 없으면 생성
    if (!_controllers.containsKey(productId)) {
      _controllers[productId] = TextEditingController();
    }

    final controller = _controllers[productId]!;
    final currentQuantity = _serviceQuantities[productId] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 상품 정보
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontFamily: 'Pretendard',
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '판매자: ${product.sellerName ?? "미지정"}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        '재고: ${product.quantity}개',
                        style: TextStyle(
                          color: product.quantity > 0 ? Colors.green[600] : Colors.red[600],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '가격: ${CurrencyUtils.formatCurrency(product.price)}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // 수량 입력
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  // 감소 버튼
                  IconButton(
                    onPressed: currentQuantity > 0 ? () => _updateQuantity(productId, currentQuantity - 1) : null,
                    icon: const Icon(Icons.remove),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                  
                  // 수량 입력 필드
                  Expanded(
                    child: TextField(
                      controller: controller,
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                        hintText: '0',
                      ),
                      onChanged: (value) {
                        final quantity = int.tryParse(value) ?? 0;
                        _updateQuantity(productId, quantity);
                      },
                    ),
                  ),
                  
                  // 증가 버튼
                  IconButton(
                    onPressed: currentQuantity < product.quantity ? () => _updateQuantity(productId, currentQuantity + 1) : null,
                    icon: const Icon(Icons.add),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.blue[100],
                      foregroundColor: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 수량 업데이트
  void _updateQuantity(int productId, int quantity) {
    final product = ref.read(productNotifierProvider).products.firstWhere((p) => p.id == productId);
    
    // 재고보다 많이 입력할 수 없음
    if (quantity > product.quantity) {
      quantity = product.quantity;
    }
    
    // 음수는 0으로 처리
    if (quantity < 0) {
      quantity = 0;
    }

    setState(() {
      _serviceQuantities[productId] = quantity;
      _controllers[productId]?.text = quantity.toString();
    });
  }

  /// 선택된 상품이 있는지 확인
  bool _hasSelectedProducts() {
    return _serviceQuantities.values.any((quantity) => quantity > 0);
  }

  /// 총 선택된 수량 계산
  int _getTotalSelectedQuantity() {
    return _serviceQuantities.values.fold(0, (sum, quantity) => sum + quantity);
  }

  /// 서비스 처리
  Future<void> _processService() async {
    if (!_hasSelectedProducts()) {
      ToastUtils.showError(context, '서비스 처리할 상품을 선택해주세요.');
      return;
    }

    // 확인 다이얼로그 표시
    final confirmed = await _showServiceConfirmDialog();
    if (confirmed != true) return;

    try {
      // 서비스 처리 실행
      await ref.read(serviceNotifierProvider.notifier).processService(_serviceQuantities);
      
      // 성공 시 상태 초기화
      setState(() {
        _serviceQuantities.clear();
        for (final controller in _controllers.values) {
          controller.clear();
        }
      });
      
      if (mounted) {
        ToastUtils.showToast(context, '서비스 처리가 완료되었습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('서비스 처리 실패', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '서비스 처리 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 서비스 처리 확인 다이얼로그
  Future<bool?> _showServiceConfirmDialog() async {
    final selectedProducts = <String>[];
    final allProducts = ref.read(productNotifierProvider).products;
    
    for (final entry in _serviceQuantities.entries) {
      if (entry.value > 0) {
        final product = allProducts.firstWhere((p) => p.id == entry.key,
          orElse: () => throw Exception('상품을 찾을 수 없습니다: ID ${entry.key}'));
        selectedProducts.add('${product.name} x${entry.value}');
      }
    }

    return await ConfirmationDialog.show(
      context: context,
      title: '서비스 처리 확인',
      message: [
        '다음 상품들을 서비스 처리하시겠습니까?',
        '',
        selectedProducts.join('\n'),
        '',
        '• 재고가 차감됩니다.',
        '• 판매 금액은 0원으로 처리됩니다.',
        '• 판매 기록에 "서비스"로 표시됩니다.',
      ].join('\n'),
      confirmLabel: '서비스 처리',
      cancelLabel: '취소',
    );
  }

  /// 도움말 다이얼로그
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('서비스 처리 도움말'),
        content: const Text(
          '서비스 처리는 상품을 무료로 제공할 때 사용합니다.\n\n'
          '• 재고는 실제로 차감됩니다.\n'
          '• 판매 금액은 0원으로 기록됩니다.\n'
          '• 판매 기록에 "서비스"로 표시됩니다.\n'
          '• 통계에서 서비스 제공 내역을 확인할 수 있습니다.\n\n'
          '각 상품의 수량을 입력하고 "서비스 처리" 버튼을 눌러주세요.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }
}
