import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product.dart';
import '../models/seller.dart';
import '../models/prepayment.dart';
import '../models/sales_log.dart';
import '../models/set_discount.dart';
import '../providers/fallback_data_provider.dart';
import '../providers/current_event_provider.dart';
import '../providers/realtime_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
import '../utils/logger_utils.dart';

/// 기존 Provider와 새로운 실시간 동기화 시스템 간의 브리지
/// 
/// 실제로 작동하는 실시간 동기화 기능을 제공합니다.
/// 실시간 동기화가 실패할 경우 자동으로 로컬 데이터 폴백을 사용합니다.

// realtimeSyncServiceProvider는 realtime_sync_provider.dart에서 정의됨

/// 실시간 데이터 상태 모델
class RealtimeEventData {
  final List<Product> products;
  final List<Seller> sellers;
  final List<Prepayment> prepayments;
  final List<SalesLog> salesLogs;
  final List<SetDiscount> setDiscounts;
  final bool isLoading;
  final bool isConnected;
  final String? errorMessage;

  const RealtimeEventData({
    this.products = const [],
    this.sellers = const [],
    this.prepayments = const [],
    this.salesLogs = const [],
    this.setDiscounts = const [],
    this.isLoading = false,
    this.isConnected = false,
    this.errorMessage,
  });

  RealtimeEventData copyWith({
    List<Product>? products,
    List<Seller>? sellers,
    List<Prepayment>? prepayments,
    List<SalesLog>? salesLogs,
    List<SetDiscount>? setDiscounts,
    bool? isLoading,
    bool? isConnected,
    String? errorMessage,
  }) {
    return RealtimeEventData(
      products: products ?? this.products,
      sellers: sellers ?? this.sellers,
      prepayments: prepayments ?? this.prepayments,
      salesLogs: salesLogs ?? this.salesLogs,
      setDiscounts: setDiscounts ?? this.setDiscounts,
      isLoading: isLoading ?? this.isLoading,
      isConnected: isConnected ?? this.isConnected,
      errorMessage: errorMessage,
    );
  }
}

/// 현재 이벤트의 실시간 데이터 Provider
final currentEventRealtimeDataProvider = StateNotifierProvider<CurrentEventRealtimeNotifier, RealtimeEventData>((ref) {
  final syncService = ref.watch(realtimeSyncServiceProvider);
  final currentEventId = ref.watch(currentEventIdProvider);
  
  return CurrentEventRealtimeNotifier(
    syncService: syncService,
    eventId: currentEventId,
    ref: ref,
  );
});

/// 현재 이벤트 실시간 데이터 관리자
class CurrentEventRealtimeNotifier extends StateNotifier<RealtimeEventData> {
  static const String _tag = 'CurrentEventRealtimeNotifier';

  final RealtimeSyncService syncService;
  final int? eventId;
  final Ref ref;

  // 메모리 누수 방지를 위한 리소스 추적
  StreamSubscription? _dataStreamSubscription;
  VoidCallback? _connectionListener;

  CurrentEventRealtimeNotifier({
    required this.syncService,
    required this.eventId,
    required this.ref,
  }) : super(const RealtimeEventData()) {
    _initialize();
  }

  Future<void> _initialize() async {
    if (eventId == null) {
      LoggerUtils.logWarning('이벤트 ID가 없어 실시간 동기화를 시작할 수 없습니다', tag: _tag);
      return;
    }

    try {
      state = state.copyWith(isLoading: true);
      
      // 실시간 동기화 서비스 초기화
      if (!syncService.isInitialized.value) {
        await syncService.initialize();
      }
      
      // 실시간 동기화 구독 시작
      await syncService.subscribeToEvent(eventId!);
      
      // 실시간 데이터 스트림 구독
      final dataStream = syncService.getEventDataStream(eventId!);
      if (dataStream != null) {
        _dataStreamSubscription = dataStream.listen((eventData) {
          LoggerUtils.logInfo('실시간 데이터 업데이트: 상품 ${eventData.products.length}개', tag: _tag);
          state = state.copyWith(
            products: eventData.products,
            sellers: eventData.sellers,
            prepayments: eventData.prepayments,
            salesLogs: eventData.salesLogs,
            setDiscounts: eventData.setDiscounts,
            isLoading: false,
            isConnected: true,
            errorMessage: null,
          );
        });
      }

      // 연결 상태 모니터링
      _connectionListener = () {
        state = state.copyWith(isConnected: syncService.isConnected.value);
      };
      syncService.isConnected.addListener(_connectionListener!);
      
      LoggerUtils.logInfo('이벤트 $eventId 실시간 동기화 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 초기화 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        isConnected: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 리소스 정리 (메모리 누수 방지)
  @override
  void dispose() {
    // 실시간 데이터 스트림 구독 해제
    _dataStreamSubscription?.cancel();
    _dataStreamSubscription = null;

    // 연결 상태 리스너 제거
    if (_connectionListener != null) {
      syncService.isConnected.removeListener(_connectionListener!);
      _connectionListener = null;
    }

    LoggerUtils.logDebug('CurrentEventRealtimeNotifier disposed - 메모리 누수 방지 완료', tag: _tag);
    super.dispose();
  }

  /// 상품 추가
  Future<void> addProduct(Product product) async {
    if (eventId == null) {
      throw Exception('이벤트 ID가 없습니다.');
    }
    
    try {
      LoggerUtils.logInfo('상품 추가: ${product.name}', tag: _tag);
      await syncService.addProduct(eventId!, product);
    } catch (e) {
      LoggerUtils.logError('상품 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품 수정
  Future<void> updateProduct(Product product) async {
    if (eventId == null) {
      throw Exception('이벤트 ID가 없습니다.');
    }
    
    try {
      LoggerUtils.logInfo('상품 수정: ${product.name}', tag: _tag);
      await syncService.updateProduct(eventId!, product);
    } catch (e) {
      LoggerUtils.logError('상품 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품 삭제
  Future<void> deleteProduct(int productId) async {
    if (eventId == null) {
      throw Exception('이벤트 ID가 없습니다.');
    }
    
    try {
      LoggerUtils.logInfo('상품 삭제: $productId', tag: _tag);
      await syncService.deleteProduct(eventId!, productId);
    } catch (e) {
      LoggerUtils.logError('상품 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매자 추가
  Future<void> addSeller(Seller seller) async {
    if (eventId == null) {
      throw Exception('이벤트 ID가 없습니다.');
    }
    
    try {
      LoggerUtils.logInfo('판매자 추가: ${seller.name}', tag: _tag);
      await syncService.addSeller(eventId!, seller);
    } catch (e) {
      LoggerUtils.logError('판매자 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 추가
  Future<void> addPrepayment(Prepayment prepayment) async {
    if (eventId == null) {
      throw Exception('이벤트 ID가 없습니다.');
    }
    
    try {
      LoggerUtils.logInfo('선입금 추가: ${prepayment.id}', tag: _tag);
      await syncService.addPrepayment(eventId!, prepayment);
    } catch (e) {
      LoggerUtils.logError('선입금 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

/// 실시간 상품 브리지 Provider (폴백 포함)
final realtimeProductBridgeProvider = Provider<List<Product>>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  final fallbackData = ref.watch(fallbackProductsProvider);
  
  // 실시간 데이터가 있고 연결되어 있으면 실시간 데이터 사용
  if (realtimeData.isConnected && !realtimeData.isLoading && realtimeData.errorMessage == null) {
    LoggerUtils.logDebug('실시간 상품 데이터 사용: ${realtimeData.products.length}개', tag: 'RealtimeBridge');
    return realtimeData.products;
  }
  
  // 실시간 데이터가 없으면 폴백 데이터 사용
  if (fallbackData.isNotEmpty) {
    LoggerUtils.logDebug('폴백 상품 데이터 사용: ${fallbackData.length}개', tag: 'RealtimeBridge');
    return fallbackData;
  }
  
  // 모두 없으면 실시간 데이터 반환 (빈 리스트라도)
  LoggerUtils.logDebug('실시간 상품 데이터 반환 (기본): ${realtimeData.products.length}개', tag: 'RealtimeBridge');
  return realtimeData.products;
});

/// 실시간 판매자 브리지 Provider
final realtimeSellerBridgeProvider = Provider<List<Seller>>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.sellers;
});

/// 실시간 선입금 브리지 Provider
final realtimePrepaymentBridgeProvider = Provider<List<Prepayment>>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.prepayments;
});

/// 실시간 판매 로그 브리지 Provider
final realtimeSalesLogBridgeProvider = Provider<List<SalesLog>>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.salesLogs;
});

/// 실시간 세트 할인 브리지 Provider
final realtimeSetDiscountBridgeProvider = Provider<List<SetDiscount>>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.setDiscounts;
});

/// 기존 ProductState와 호환되는 상태 클래스
class RealtimeProductState {
  final List<Product> products;
  final bool isLoading;
  final String? errorMessage;
  final bool isConnected;

  const RealtimeProductState({
    required this.products,
    required this.isLoading,
    this.errorMessage,
    required this.isConnected,
  });

  // 기존 ProductState와 동일한 인터페이스 제공
  List<Product> get filteredProducts => products;
  int get totalProducts => products.length;
  bool get hasError => errorMessage != null;
}

/// 기존 ProductNotifier와 호환되는 브리지 Provider
final realtimeProductStateProvider = Provider<RealtimeProductState>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  
  return RealtimeProductState(
    products: realtimeData.products,
    isLoading: realtimeData.isLoading,
    errorMessage: realtimeData.errorMessage,
    isConnected: realtimeData.isConnected,
  );
});

/// 개별 상품 조회 Provider (실시간 대응)
final realtimeProductByIdProvider = Provider.family<Product?, int>((ref, productId) {
  final products = ref.watch(realtimeProductBridgeProvider);
  try {
    return products.firstWhere((product) => product.id == productId);
  } catch (e) {
    return null;
  }
});

/// 실시간 연결 상태 Provider들
final realtimeConnectionStatusProvider = Provider<bool>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.isConnected;
});

final realtimeLoadingStatusProvider = Provider<bool>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.isLoading;
});

final realtimeErrorProvider = Provider<String?>((ref) {
  final realtimeData = ref.watch(currentEventRealtimeDataProvider);
  return realtimeData.errorMessage;
});
