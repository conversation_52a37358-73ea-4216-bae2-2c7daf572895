import 'package:sqflite/sqflite.dart';
import '../models/sales_log.dart';
import '../models/transaction_type.dart';
import '../services/database_service.dart';

import '../utils/offline_task.dart';
import '../utils/network_status.dart';

/// SalesLog의 기본 CRUD 작업을 관리하는 클래스
class SalesLogCrud {
  final DatabaseService _databaseService;
  
  SalesLogCrud({required DatabaseService database})
    : _databaseService = database;

  Future<Database> get _database => _databaseService.database;

  // 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogs() async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 특정 행사의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsByEventId(int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 특정 타입의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogsByType(
    TransactionType transactionType,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'transactionType = ?',
      whereArgs: [transactionType.value],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 판매자별 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySeller(String sellerName) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'sellerName = ?',
      whereArgs: [sellerName],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 판매자별 특정 타입의 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'sellerName = ? AND transactionType = ?',
      whereArgs: [sellerName, transactionType.value],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 모든 고유한 판매자 이름 목록 조회
  Future<List<String>> getAllDistinctSellerNames() async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      columns: ['DISTINCT sellerName'],
      where: 'sellerName IS NOT NULL AND sellerName != ""',
      orderBy: 'sellerName ASC',
    );

    return maps
        .map((map) => map['sellerName'] as String)
        .where((name) => name.isNotEmpty)
        .toList();
  }

  // 날짜 범위별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'saleTimestamp BETWEEN ? AND ?',
      whereArgs: [startTimestamp, endTimestamp],
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 배치 ID별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByBatchId(String batchId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // ID로 판매 기록 조회
  Future<SalesLog?> getLogById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SalesLog.fromMap(maps.first);
    }
    return null;
  }

  // 판매 기록 삽입
  Future<int> insertLog(SalesLog log) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.insert,
        table: DatabaseServiceImpl.salesLogTable,
        data: log.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.insert(
      DatabaseServiceImpl.salesLogTable,
      log.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // 판매 기록 업데이트
  Future<int> updateLog(SalesLog log) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.update,
        table: DatabaseServiceImpl.salesLogTable,
        data: log.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.update(
      DatabaseServiceImpl.salesLogTable,
      log.toMap(),
      where: 'id = ?',
      whereArgs: [log.id],
    );
  }

  // 판매 기록 삭제
  Future<int> deleteLog(SalesLog log) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.delete,
        table: DatabaseServiceImpl.salesLogTable,
        data: log.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [log.id],
    );
  }

  // 모든 판매 기록 삭제
  Future<int> deleteAllLogs() async {
    final db = await _database;
    return await db.delete(DatabaseServiceImpl.salesLogTable);
  }

  // 판매자별 판매 기록 삭제
  Future<int> deleteLogsBySeller(String sellerName) async {
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'sellerName = ?',
      whereArgs: [sellerName],
    );
  }

  // 날짜 범위별 판매 기록 삭제
  Future<int> deleteLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'saleTimestamp BETWEEN ? AND ?',
      whereArgs: [startTimestamp, endTimestamp],
    );
  }

  // 배치 ID별 판매 기록 삭제
  Future<int> deleteLogsByBatchId(String batchId) async {
    final db = await _database;
    return await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
    );
  }

  // 판매 기록 추가
  Future<SalesLog> addSalesLog(SalesLog salesLog) async {
    final id = await insertLog(salesLog);
    return salesLog.copyWith(id: id);
  }

  // 판매 기록 업데이트
  Future<int> updateSalesLog(SalesLog salesLog) async {
    return await updateLog(salesLog);
  }

  // 판매 기록 삭제
  Future<void> deleteSalesLog(int id) async {
    final db = await _database;
    await db.delete(
      DatabaseServiceImpl.salesLogTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
} 