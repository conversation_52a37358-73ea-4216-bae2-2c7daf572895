import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/seller_provider.dart';
import 'sales_log_list_tab.dart';
import 'sales_stats_tab.dart';


class SalesHistoryScreen extends ConsumerStatefulWidget {
  const SalesHistoryScreen({super.key});

  @override
  ConsumerState<SalesHistoryScreen> createState() => _SalesHistoryScreenState();
}

class _SalesHistoryScreenState extends ConsumerState<SalesHistoryScreen>
    with SingleTickerProviderStateMixin, RestorationMixin {
  late TabController _tabController;

  // 필터 상태
  String _selectedSeller = '전체 판매자';
  DateTimeRange? _selectedDateRange;

  @override
  String? get restorationId => 'sales_history_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      ref.read(sellerNotifierProvider.notifier).loadSellers(); // 판매자 데이터 명시적 로딩
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 화면이 다시 포커스될 때 데이터 새로고침
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // 테마 기반 색상 사용 (하드코딩 제거)
        // backgroundColor: Colors.blue, 제거 - 테마에서 자동으로 적용
        // foregroundColor: Colors.white, 제거 - 테마에서 자동으로 적용
        title: _buildToolbarContent(), // 원본과 정확히 동일한 툴바 구조
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '판매 기록'),
            Tab(text: '판매 통계'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          SalesLogListTab(
            selectedSeller: _selectedSeller,
            selectedDateRange: _selectedDateRange,
          ),
          SalesStatsTab(
            selectedSeller: _selectedSeller,
            selectedDateRange: _selectedDateRange,
          ),
        ],
      ),
    );
  }

  /// 툴바 내용 (원본 activity_sales_history.xml과 정확히 동일한 구조)
  Widget _buildToolbarContent() {
    return SizedBox(
      width: double.infinity,
      height: kToolbarHeight,
      child: Row(
        children: [
          // date_filter_container_sh (LinearLayout) - 원본과 정확히 동일
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 4,
                horizontal: 8,
              ), // 원본 padding
              decoration: BoxDecoration(
                color: Colors.transparent, // 원본 selectableItemBackground 효과
                borderRadius: BorderRadius.circular(4),
              ),
              child: InkWell(
                onTap: _showMaterialDateRangePicker,
                borderRadius: BorderRadius.circular(4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // textView_date_filter_sh (원본과 정확히 동일)
                    Flexible(
                      child: Text(
                        _getDateRangeText(),
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontFamily: 'Pretendard',
                          color: Colors.white, // 원본 colorOnPrimary
                          fontSize: 16, // 원본과 동일한 16sp
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // button_select_date_range_sh (ImageButton) - 원본과 정확히 동일
                    Container(
                      width: 24, // 원본 24dp
                      height: 24, // 원본 24dp
                      margin: const EdgeInsets.only(
                        left: 4,
                      ), // 원본 layout_marginStart="4dp"
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: const Icon(
                          Icons.event, // 원본 @drawable/ic_calendar과 유사
                          color: Colors.white, // 원본 colorOnPrimary
                          size: 16, // 24dp 컨테이너에 맞는 아이콘 크기
                        ),
                        onPressed: _showMaterialDateRangePicker,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // spinner_seller_filter_sh (원본과 정확히 동일)
          Consumer(
            builder: (context, ref, child) {
              // 실시간 판매자 목록 감시
              final sellerAsync = ref.watch(sellerNotifierProvider);
              final allSellersOption = '전체 판매자';
              final sellerNames = sellerAsync.isLoading
                  ? <String>[]
                  : sellerAsync.hasError
                      ? <String>[]
                      : sellerAsync.sellers.map((s) => s.name).toList()..sort();
              final sellers = [
                allSellersOption,
                ...sellerNames,
              ];

              return RepaintBoundary(
                child: Container(
                  constraints: const BoxConstraints(
                    minWidth: 130,
                  ), // 원본 minWidth="130dp"
                  margin: const EdgeInsets.only(
                    right: 8,
                  ), // 원본 layout_marginEnd="8dp"
                  child: DropdownButton<String>(
                    value: _selectedSeller,
                    dropdownColor: Colors.blue.shade700,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white, fontSize: 14),
                    underline: Container(), // 원본 background="@null" 효과
                    icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                    items: sellers
                        .map(
                          (seller) => DropdownMenuItem<String>(
                            value: seller,
                            child: Text(
                              seller,
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.white),
                            ),
                          ),
                        )
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSeller = value;
                        });
                      }
                    },
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 날짜 범위 텍스트 생성 (원본과 동일)
  String _getDateRangeText() {
    if (_selectedDateRange == null) {
      return '전체 기간'; // 원본과 동일
    }

    final start = _selectedDateRange!.start;
    final end = _selectedDateRange!.end;

    return '${start.year}.${start.month.toString().padLeft(2, '0')}.${start.day.toString().padLeft(2, '0')} - ${end.year}.${end.month.toString().padLeft(2, '0')}.${end.day.toString().padLeft(2, '0')}';
  }

  /// MaterialDatePicker를 띄우는 함수 (원본 SalesHistoryActivity와 정확히 동일)
  Future<void> _showMaterialDateRangePicker() async {
    final selectedStartDate = _selectedDateRange?.start.millisecondsSinceEpoch;
    final selectedEndDate = _selectedDateRange?.end.millisecondsSinceEpoch;

    // MaterialDatePicker의 기본 선택 값을 UTC 기준으로 설정 (원본과 동일)
    final defaultStart =
        selectedStartDate ??
        () {
          final now = DateTime.now();
          final oneMonthAgo = DateTime(now.year, now.month - 1, now.day);
          return oneMonthAgo.millisecondsSinceEpoch;
        }();
    final defaultEnd = selectedEndDate ?? DateTime.now().millisecondsSinceEpoch;

    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: DateTime.fromMillisecondsSinceEpoch(defaultStart),
        end: DateTime.fromMillisecondsSinceEpoch(defaultEnd),
      ),
      locale: const Locale('ko', 'KR'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Colors.blue),
          ),
          child: child!,
        );
      },
      helpText: '날짜 범위 선택', // 원본과 동일
    );

    if (dateRange != null) {
      // 선택된 날짜의 끝 시간을 포함하도록 endDate 조정 (원본과 동일)
      final adjustedEndDate = DateTime(
        dateRange.end.year,
        dateRange.end.month,
        dateRange.end.day,
        23,
        59,
        59,
        999,
      );

      setState(() {
        _selectedDateRange = DateTimeRange(
          start: dateRange.start,
          end: adjustedEndDate,
        );
      });
    }
  }
}


