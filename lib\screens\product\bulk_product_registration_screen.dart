import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:image/image.dart' as img;

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../utils/excel_processor.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';
import '../../widgets/image_crop_widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../widgets/registration_complete_page.dart';
import '../../screens/inventory/inventory_screen.dart';
import '../../providers/prepayment_provider.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../services/database_service.dart';
import '../../widgets/registration_link_prompt_page.dart';
import '../../utils/image_utils.dart';

/// 상품 일괄 등록 화면
/// 엑셀에서 추출한 상품명 리스트를 받아서 각 상품의 가격, 수량, 판매자를 일괄 입력
class BulkProductRegistrationScreen extends ConsumerStatefulWidget {
  final List<ExcelProductData> productData;
  final List<ExcelPrepaymentData> prepaymentData; // 추가
  final void Function(List<Product>)? onComplete;
  final bool strictRequired;
  final bool isCombinedRegistration;

  const BulkProductRegistrationScreen({
    super.key,
    required this.productData,
    required this.prepaymentData, // 추가
    this.onComplete,
    this.strictRequired = false,
    this.isCombinedRegistration = false,
  });

  @override
  ConsumerState<BulkProductRegistrationScreen> createState() => _BulkProductRegistrationScreenState();
}

class _BulkProductRegistrationScreenState extends ConsumerState<BulkProductRegistrationScreen> {
  final Map<String, TextEditingController> _priceControllers = {};
  final Map<String, TextEditingController> _quantityControllers = {};
  final Map<String, String?> _selectedSellers = {};
  final Map<String, XFile?> _selectedImages = {};
  final Map<String, Rect?> _cropRects = {};
  
  // FocusNode 추가
  final Map<String, FocusNode> _priceFocusNodes = {};
  final Map<String, FocusNode> _quantityFocusNodes = {};
  
  bool _isProcessing = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    
    // 대표 판매자를 기본값으로 설정
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final sellerState = ref.read(sellerNotifierProvider);
      if (sellerState.defaultSeller != null) {
        setState(() {
          for (final data in widget.productData) {
            _selectedSellers[data.name] = sellerState.defaultSeller!.name;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    // 컨트롤러들 정리
    for (final controller in _priceControllers.values) {
      controller.dispose();
    }
    for (final controller in _quantityControllers.values) {
      controller.dispose();
    }
    
    // FocusNode 해제
    for (final focusNode in _priceFocusNodes.values) {
      focusNode.dispose();
    }
    for (final focusNode in _quantityFocusNodes.values) {
      focusNode.dispose();
    }
    
    super.dispose();
  }

  /// 컨트롤러들을 초기화합니다.
  void _initializeControllers() {
    for (final data in widget.productData) {
      _priceControllers[data.name] = TextEditingController();
      _quantityControllers[data.name] = TextEditingController();
      _selectedSellers[data.name] = null;
      _selectedImages[data.name] = null;
      _cropRects[data.name] = null;
      
      // FocusNode 초기화
      _priceFocusNodes[data.name] = FocusNode();
      _quantityFocusNodes[data.name] = FocusNode();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '상품 일괄 등록',
          style: TextStyle(
            color: OnboardingColors.textOnPrimary,
            fontSize: isTablet ? 20.0 : 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: OnboardingColors.primaryGradient,
          ),
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: OnboardingColors.textOnPrimary,
        elevation: 0,
        actions: [
          if (widget.productData.isNotEmpty) ...[
            Container(
              margin: EdgeInsets.only(right: isTablet ? 8.0 : 4.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    OnboardingColors.accent,
                    OnboardingColors.accentLight,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: OnboardingColors.accent.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: _isProcessing ? null : _showQuickInputDialog,
                  child: Container(
                    padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
                    child: Icon(
                      Icons.flash_on_rounded,
                      color: OnboardingColors.textOnPrimary,
                      size: isTablet ? 24.0 : 20.0,
                    ),
                  ),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(right: isTablet ? 16.0 : 8.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    OnboardingColors.success,
                    OnboardingColors.successLight,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: OnboardingColors.success.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: _isProcessing ? null : _registerAllProducts,
                  child: Container(
                    padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
                    child: Icon(
                      Icons.save_rounded,
                      color: OnboardingColors.textOnPrimary,
                      size: isTablet ? 24.0 : 20.0,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 요약 정보
            _buildSummaryInfo(),
            // 상품 리스트
            Expanded(
              child: _buildProductList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 요약 정보를 구성합니다.
  Widget _buildSummaryInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '총 ${widget.productData.length}개의 상품을 등록할 수 있습니다.',
            style: TextStyle(fontFamily: 'Pretendard', 
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '각 상품의 가격, 수량, 판매자를 입력해주세요.',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 상품 리스트를 구성합니다.
  Widget _buildProductList() {
    return Scrollbar(
      thumbVisibility: true,
      trackVisibility: true,
      child: ListView.builder(
        itemCount: widget.productData.length,
        itemBuilder: (context, index) {
          final data = widget.productData[index];
          final priceController = _priceControllers[data.name];
          final quantityController = _quantityControllers[data.name];
          final selectedSeller = _selectedSellers[data.name];
          final selectedImage = _selectedImages[data.name];

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 상품명
                  Text(
                    data.name,
                    style: TextStyle(fontFamily: 'Pretendard', 
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // 입력 필드들
                  Row(
                    children: [
                      // 가격 입력
                      Expanded(
                        child: TextField(
                          controller: priceController,
                          focusNode: _priceFocusNodes[data.name],
                          textInputAction: TextInputAction.next,
                          onSubmitted: (value) {
                            // 현재 상품의 수량 필드로 이동
                            _quantityFocusNodes[data.name]?.requestFocus();
                          },
                          decoration: const InputDecoration(
                            labelText: '가격',
                            suffixText: '원',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      // 수량 입력
                      Expanded(
                        child: TextField(
                          controller: quantityController,
                          focusNode: _quantityFocusNodes[data.name],
                          textInputAction: index == widget.productData.length - 1 
                              ? TextInputAction.done 
                              : TextInputAction.next,
                          onSubmitted: (value) {
                            if (index < widget.productData.length - 1) {
                              // 다음 상품의 가격 필드로 이동
                              final nextProductName = widget.productData[index + 1].name;
                              _priceFocusNodes[nextProductName]?.requestFocus();
                            } else {
                              // 마지막 상품이면 포커스 해제
                              _quantityFocusNodes[data.name]?.unfocus();
                            }
                          },
                          decoration: const InputDecoration(
                            labelText: '수량',
                            suffixText: '개',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // 판매자 선택
                  Consumer(
                    builder: (context, ref, child) {
                      final sellerState = ref.watch(sellerNotifierProvider);
                      
                      if (sellerState.isLoading) {
                        return const CircularProgressIndicator();
                      }
                      
                      if (sellerState.errorMessage != null) {
                        return Text('오류: ${sellerState.errorMessage}');
                      }
                      
                      return DropdownButtonFormField<String>(
                        value: selectedSeller,
                        decoration: const InputDecoration(
                          labelText: '판매자 선택',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('판매자를 선택하세요'),
                          ),
                          ...sellerState.sellers.map((seller) {
                            final isDefault = seller.isDefault;
                            return DropdownMenuItem<String>(
                              value: seller.name,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isDefault) 
                                    const Icon(Icons.star, color: Colors.amber, size: 16),
                                  const SizedBox(width: 8),
                                  Flexible(
                                    child: Text(
                                      seller.name,
                                      style: TextStyle(
                                        fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                  if (isDefault)
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.amber.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Text(
                                        '대표',
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: Colors.amber,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedSellers[data.name] = value;
                          });
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // 이미지 선택
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _pickImage(data.name),
                          icon: const Icon(Icons.image),
                          label: const Text('이미지 선택'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      if (selectedImage != null)
                        Expanded(
                          child: Text(
                            '이미지 선택됨',
                            style: TextStyle(fontFamily: 'Pretendard', 
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 이미지를 선택합니다.
  Future<void> _pickImage(String productName) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      
      if (image != null) {
        // 1. 흰색 캔버스+중앙 배치 적용
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await getTemporaryDirectory();
        final paddedPath = path.join(tempDir.path, 'padded_${productName}_${DateTime.now().millisecondsSinceEpoch}.jpg');
        final paddedFile = await File(paddedPath).writeAsBytes(paddedBytes);
        
        setState(() {
          _selectedImages[productName] = XFile(paddedFile.path);
        });
        
        // 2. 크롭 다이얼로그(라운드 사각형, 1:1, 오버레이/로딩/원본노출방지)
        _showImageCropDialog(productName, paddedFile.path);
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 중 오류', error: e);
      ToastUtils.showError(context, '이미지 선택 중 오류가 발생했습니다.');
    }
  }

  /// 이미지 크롭 다이얼로그를 표시합니다.
  Future<void> _showImageCropDialog(String productName, String imagePath) async {
    try {
      final croppedFile = await ImageCropUtils.cropImage(
        context: context,
        imagePath: imagePath,
        shape: CropShape.roundedSquare,
        aspectRatio: 1.0,
      );

      if (croppedFile != null) {
        final appDir = await getApplicationDocumentsDirectory();
        final tempDir = Directory(path.join(appDir.path, 'temp_crops'));
        if (!await tempDir.exists()) {
          await tempDir.create(recursive: true);
        }
        
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final tempFileName = 'temp_crop_${productName}_$timestamp.jpg';
        final tempFile = File(path.join(tempDir.path, tempFileName));
        await croppedFile.copy(tempFile.path);
        setState(() {
          _selectedImages[productName] = XFile(tempFile.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 크롭 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  /// 이미지를 저장합니다.
  Future<String?> _saveImageToInternalStorage(String productName) async {
    try {
      final selectedImage = _selectedImages[productName];
      if (selectedImage == null) return null;

      final appDir = await getApplicationDocumentsDirectory();
      final productImagesDir = Directory(
        path.join(appDir.path, 'product_images'),
      );

      if (!await productImagesDir.exists()) {
        await productImagesDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'SOGOM_IMG_${productName}_$timestamp.jpg';
      final savedImage = File(path.join(productImagesDir.path, fileName));

      // 이미지 바이트 읽기
      final imageBytes = await selectedImage.readAsBytes();
      final img.Image? imgDecoded = img.decodeImage(imageBytes);
      final img.Image imgResized = img.copyResize(imgDecoded!, width: 400, height: 400);
      final jpgBytes = img.encodeJpg(imgResized, quality: 80);
      await savedImage.writeAsBytes(jpgBytes);
      return savedImage.path;
    } catch (e) {
      LoggerUtils.logError('이미지 저장 중 오류 (상품명: $productName)', error: e);
      return null;
    }
  }

  /// 모든 상품을 등록합니다.
  Future<void> _registerAllProducts() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      // 현재 선택된 행사 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logError('현재 워크스페이스가 null입니다', tag: 'BulkProductRegistration');
        ToastUtils.showError(context, '현재 선택된 행사 워크스페이스가 없습니다. 행사 워크스페이스를 선택해주세요.');
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      LoggerUtils.logInfo('워크스페이스 확인 완료: ${currentWorkspace.name} (ID: ${currentWorkspace.id})', tag: 'BulkProductRegistration');

      LoggerUtils.logInfo('상품 등록 시작 - 현재 행사 워크스페이스: ${currentWorkspace.name} (ID: ${currentWorkspace.id})', tag: 'BulkProductRegistration');

      // [신규] 1. 임시 선입금 데이터 등록
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);
      final tempPrepayments = prepaymentNotifier.getTempPrepaymentList();
      for (final prepayment in tempPrepayments) {
        try {
          await prepaymentNotifier.addPrepayment(prepayment);
        } catch (e) {
          // 에러 발생 시 로그 출력 또는 필요한 처리
        }
      }
      // 임시 데이터 삭제
      await prepaymentNotifier.clearTempPrepaymentData();

      // [신규] 1-2. 선입금 등록 후 가상상품 데이터 누적/업데이트
      try {
        final databaseService = ref.read(databaseServiceProvider);
        final db = await databaseService.database;
        // 1. 모든 선입금 데이터에서 가상상품별 총수량 집계
        final Map<String, int> virtualProductQuantityMap = {};
        for (final prepayment in tempPrepayments) {
          for (final product in prepayment.purchasedProducts) {
            final vpName = product.name.trim();
            if (vpName.isEmpty) continue;
            final vpNameLower = vpName.toLowerCase();
            virtualProductQuantityMap[vpNameLower] = (virtualProductQuantityMap[vpNameLower] ?? 0) + product.quantity;
          }
        }
        // 2. DB에서 현재 행사의 기존 가상상품 목록 조회
        final allVirtualProducts = await db.query(
          'prepayment_virtual_product',
          where: 'eventId = ?',
          whereArgs: [currentWorkspace.id],
        );
        final Map<String, Map<String, dynamic>> existingVirtualProducts = {
          for (final e in allVirtualProducts) (e['name'] as String).trim().toLowerCase(): e
        };
        // 3. insert/update (수량 누적, createdAt/updatedAt 관리)
        for (final entry in virtualProductQuantityMap.entries) {
          final vpNameLower = entry.key;
          final quantityToAdd = entry.value;
          if (existingVirtualProducts.containsKey(vpNameLower)) {
            // 이미 존재: 수량 누적 update
            final existing = existingVirtualProducts[vpNameLower]!;
            final newQuantity = (existing['quantity'] as int) + quantityToAdd;
            await db.update(
              'prepayment_virtual_product',
              {
                'quantity': newQuantity,
                'updatedAt': DateTime.now().toIso8601String(),
              },
              where: 'name = ? AND eventId = ?',
              whereArgs: [existing['name'], currentWorkspace.id],
            );
          } else {
            // 신규: insert (eventId 포함)
            await db.insert('prepayment_virtual_product', {
              'name': vpNameLower, // 소문자 저장(중복 방지)
              'price': 0.0,
              'quantity': quantityToAdd,
              'createdAt': DateTime.now().toIso8601String(),
              'updatedAt': null,
              'eventId': currentWorkspace.id, // 현재 행사 ID 추가
            });
          }
        }
        // Provider에 DB 최신 데이터 즉시 반영
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
      } catch (e) {
        LoggerUtils.logError('가상상품 일괄 저장(누적) 중 오류', error: e, tag: 'BulkProductRegistration');
      }

      // [기존] 2. 상품 등록
      final repository = ref.read(productRepositoryProvider);
      final notifier = ref.read(productNotifierProvider.notifier);

      LoggerUtils.logInfo('상품 등록 시작: ${widget.productData.length}개 데이터');
      LoggerUtils.logInfo('현재 워크스페이스: ${currentWorkspace.name} (ID: ${currentWorkspace.id})', tag: 'BulkProductRegistration');

      // 먼저 모든 상품 데이터를 검증하고 준비
      List<Product> productsToAdd = [];
      List<String> incompleteProducts = [];

      for (final data in widget.productData) {
        try {
          // 필수 데이터 검증
          final priceController = _priceControllers[data.name];
          final quantityController = _quantityControllers[data.name];
          final selectedSeller = _selectedSellers[data.name];

          bool isIncomplete = false;

          if (priceController?.text.isEmpty ?? true) {
            LoggerUtils.logWarning('가격이 입력되지 않음: ${data.name}');
            isIncomplete = true;
          }

          if (quantityController?.text.isEmpty ?? true) {
            LoggerUtils.logWarning('수량이 입력되지 않음: ${data.name}');
            isIncomplete = true;
          }

          if (selectedSeller == null || selectedSeller.isEmpty) {
            LoggerUtils.logWarning('판매자가 선택되지 않음: ${data.name}');
            isIncomplete = true;
          }

          if (isIncomplete) {
            incompleteProducts.add(data.name);
            continue;
          }

          final price = int.tryParse(priceController!.text) ?? 0;
          final quantity = int.tryParse(quantityController!.text) ?? 0;

          if (price <= 0) {
            LoggerUtils.logWarning('가격이 0 이하: ${data.name}');
            incompleteProducts.add(data.name);
            continue;
          }

          if (quantity <= 0) {
            LoggerUtils.logWarning('수량이 0 이하: ${data.name}');
            incompleteProducts.add(data.name);
            continue;
          }

          // 이미지 저장
          final imagePath = await _saveImageToInternalStorage(data.name);
          
          // 새 상품 생성 (id는 AUTOINCREMENT로 자동 생성)
          final product = Product(
            name: data.name,
            quantity: quantity,
            price: price,
            sellerName: selectedSeller,
            imagePath: imagePath,
            lastServicedDate: null,
            isActive: true,
            focusX: 0.5,
            focusY: 0.5,
            eventId: currentWorkspace.id, // 현재 행사 워크스페이스 ID 설정
          );

          productsToAdd.add(product);
          LoggerUtils.logInfo('상품 데이터 준비 완료: ${data.name} (가격: $price원, 수량: $quantity개)');
        } catch (e) {
          LoggerUtils.logError(
            '상품 데이터 준비 중 오류 (상품명: ${data.name})',
            error: e,
          );
          incompleteProducts.add(data.name);
        }
      }

      LoggerUtils.logInfo('상품 데이터 준비 완료: ${productsToAdd.length}개 등록 가능, ${incompleteProducts.length}개 미완성');

      // 선입금+상품 동시 등록 모드일 때는 모든 필수값이 입력되어야 함
      if (widget.isCombinedRegistration && incompleteProducts.isNotEmpty) {
        LoggerUtils.logWarning('선입금+상품 동시 등록 모드에서 미완성 상품 발견: ${incompleteProducts.join(', ')}', tag: 'BulkProductRegistration');
        ToastUtils.showError(context, '선입금+상품 동시 등록에서는 모든 상품의 필수 정보(가격, 수량, 판매자)를 입력해야 합니다.\n\n미완성 상품: ${incompleteProducts.join(', ')}');
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      // 일반 모드일 때만 미완성 상품 확인 다이얼로그 표시
      if (!widget.isCombinedRegistration && incompleteProducts.isNotEmpty) {
        final shouldContinue = await _showConfirmationDialog(
          incompleteProducts.length,
          productsToAdd.length,
        );
        
        if (!shouldContinue) {
          setState(() {
            _isProcessing = false;
          });
          return;
        }
      }

      // 등록할 상품이 없으면 종료
      if (productsToAdd.isEmpty) {
        final errorMessage = widget.isCombinedRegistration 
            ? '선입금+상품 동시 등록에서는 모든 상품의 필수 정보를 입력해야 합니다.'
            : '등록할 수 있는 상품이 없습니다. 모든 필수 정보를 입력해주세요.';
        ToastUtils.showError(context, errorMessage);
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      int actualSuccessCount = 0;
      int actualErrorCount = 0;

      // 배치로 상품 등록
      LoggerUtils.logInfo('상품 등록 시작: ${productsToAdd.length}개', tag: 'BulkProductRegistration');
      LoggerUtils.logDebug('등록할 상품 목록: ${productsToAdd.map((p) => '${p.name}(${p.price}원, ${p.quantity}개)').join(', ')}', tag: 'BulkProductRegistration');
      
      // 현재 행사의 기존 상품 목록을 미리 가져와서 중복 체크
      final existingProducts = await repository.getProductsByEventId(currentWorkspace.id);
      final existingProductNames = existingProducts.map((p) => p.name).toSet();

      LoggerUtils.logInfo('현재 행사(${currentWorkspace.name})의 기존 상품 수: ${existingProducts.length}개', tag: 'BulkProductRegistration');
      LoggerUtils.logDebug('현재 행사의 기존 상품명 목록: ${existingProductNames.join(', ')}', tag: 'BulkProductRegistration');
      
      // 새로 등록된 상품명들을 추적하기 위한 Set
      final newlyAddedProductNames = <String>{};
      
      for (int i = 0; i < productsToAdd.length; i++) {
        final product = productsToAdd[i];
        try {
          LoggerUtils.logDebug('상품 등록 처리 시작 (${i + 1}/${productsToAdd.length})', tag: 'BulkProductRegistration');
          LoggerUtils.logDebug('처리할 상품: ${product.name} (가격: ${product.price}원, 수량: ${product.quantity}개)', tag: 'BulkProductRegistration');
          LoggerUtils.logDebug('현재 성공: $actualSuccessCount, 실패: $actualErrorCount', tag: 'BulkProductRegistration');
          
          // 기존 상품과 중복 체크 (새로 등록된 상품들도 포함)
          if (existingProductNames.contains(product.name) || newlyAddedProductNames.contains(product.name)) {
            LoggerUtils.logDebug('중복 상품 발견: ${product.name} - 건너뛰기', tag: 'BulkProductRegistration');
            LoggerUtils.logDebug('기존 상품명 목록에 포함: ${existingProductNames.contains(product.name)}', tag: 'BulkProductRegistration');
            LoggerUtils.logDebug('새로 등록된 상품명 목록에 포함: ${newlyAddedProductNames.contains(product.name)}', tag: 'BulkProductRegistration');
            actualErrorCount++;
            continue;
          }
          
          LoggerUtils.logDebug('중복 체크 통과 - 상품 등록 시도', tag: 'BulkProductRegistration');

          // ProductNotifier를 통해 상품 등록 (eventId 자동 설정됨)
          await notifier.addProduct(product);
          actualSuccessCount++;
          newlyAddedProductNames.add(product.name); // 새로 등록된 상품명 추가
          LoggerUtils.logInfo('상품 등록 성공: ${product.name} (총 성공: $actualSuccessCount개)', tag: 'BulkProductRegistration');
        } catch (e) {
          actualErrorCount++;
          LoggerUtils.logError('상품 등록 중 오류 (상품명: ${product.name})', tag: 'BulkProductRegistration', error: e);
          LoggerUtils.logError('상품 등록 실패 상세 정보 - 상품: ${product.name}, 가격: ${product.price}, 수량: ${product.quantity}, 판매자: ${product.sellerName}, eventId: ${product.eventId}', tag: 'BulkProductRegistration');
        }
        
        // 각 반복 후 현재 상태 로깅
        LoggerUtils.logDebug('반복 ${i + 1} 완료 - 성공: $actualSuccessCount, 실패: $actualErrorCount', tag: 'BulkProductRegistration');
        LoggerUtils.logDebug('상품 등록 처리 완료 (${i + 1}/${productsToAdd.length})', tag: 'BulkProductRegistration');
      }
      
      LoggerUtils.logInfo('상품 등록 완료: $actualSuccessCount개 성공, $actualErrorCount개 실패', tag: 'BulkProductRegistration');
      LoggerUtils.logDebug('새로 등록된 상품명들: ${newlyAddedProductNames.join(', ')}', tag: 'BulkProductRegistration');
      
      // 실제 DB에서 등록된 상품 수 확인
      final finalProductCount = await repository.getAllProducts();
      LoggerUtils.logInfo('DB에 실제 등록된 총 상품 수: ${finalProductCount.length}개', tag: 'BulkProductRegistration');
      
      // 단순화된 상태 업데이트
      try {
        LoggerUtils.logInfo('상품 목록 새로고침 시작');
        await notifier.loadProducts(showLoading: false);
        LoggerUtils.logInfo('상품 목록 새로고침 완료');
      } catch (e) {
        LoggerUtils.logError(
          '상품 목록 새로고침 중 오류',
          error: e,
        );
      }

      // 실제 등록된 개수로 메시지 표시
      String message;
      if (actualErrorCount > 0) {
        message = '$actualSuccessCount개의 상품이 등록되었습니다. ($actualErrorCount개 실패)';
        LoggerUtils.logInfo('최종 메시지: $message', tag: 'BulkProductRegistration');
        ToastUtils.showError(context, message);
      } else if (incompleteProducts.isNotEmpty) {
        message = '$actualSuccessCount개의 상품이 등록되었습니다. (${incompleteProducts.length}개 미완성으로 제외)';
        LoggerUtils.logInfo('최종 메시지: $message', tag: 'BulkProductRegistration');
        ToastUtils.showError(context, message);
      } else {
        message = '$actualSuccessCount개의 상품이 등록되었습니다.';
        LoggerUtils.logInfo('최종 메시지: $message', tag: 'BulkProductRegistration');
        // 정상 등록 시 토스트 메시지 제거
      }
      // 상품 등록 완료 후
      if (mounted) {
        if (widget.isCombinedRegistration && widget.prepaymentData.isNotEmpty) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => RegistrationLinkPromptPage(
                prepaymentData: widget.prepaymentData,
              ),
            ),
            (route) => false,
          );
        } else {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => RegistrationCompletePage(
                description: message,
                onConfirm: () {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const InventoryScreen()),
                    (route) => false,
                  );
                },
              ),
            ),
            (route) => false,
          );
        }
      }
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '상품 일괄 등록 중 오류',
        error: e,
        stackTrace: stackTrace,
      );
      if (mounted) {
        ToastUtils.showError(context, '등록 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 확인 다이얼로그를 표시합니다.
  Future<bool> _showConfirmationDialog(int incompleteCount, int completeCount) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            '상품 등록 확인',
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '총 ${widget.productData.length}개 상품 중:',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                '• 등록 가능: $completeCount개',
                style: TextStyle(fontFamily: 'Pretendard', 
                  fontSize: 14,
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '• 데이터 미완성: $incompleteCount개',
                style: TextStyle(fontFamily: 'Pretendard', 
                  fontSize: 14,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                '데이터가 미완성인 상품들은 등록되지 않습니다.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              Text(
                '$completeCount개의 상품만 등록하시겠습니까?',
                style: TextStyle(fontFamily: 'Pretendard', 
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                '취소',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  void _showQuickInputDialog() {
    final priceController = TextEditingController();
    final quantityController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('모든 상품에 일괄 입력'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: priceController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: '모든 가격',
                hintText: '예: 10000',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: '모든 수량',
                hintText: '예: 10',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              final price = int.tryParse(priceController.text);
              final quantity = int.tryParse(quantityController.text);
              setState(() {
                if (price != null) {
                  for (final c in _priceControllers.values) {
                    c.text = price.toString();
                  }
                }
                if (quantity != null) {
                  for (final c in _quantityControllers.values) {
                    c.text = quantity.toString();
                  }
                }
              });
              Navigator.of(context).pop();
            },
            child: const Text('적용'),
          ),
        ],
      ),
    );
  }
} 

