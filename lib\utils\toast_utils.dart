import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'logger_utils.dart';

/// 토스트/스낵바/피드백 메시지 표시를 지원하는 유틸리티 클래스입니다.
/// - 성공/오류/정보/경고/커스텀 메시지, 액션 버튼, 아이콘 등 지원
/// - riverpod 3.x Provider/Repository와 연동, 사용자 피드백/UX 개선 목적
/// - 기본 SnackBar 기반으로 안정적인 토스트 메시지 표시
class ToastUtils {
  /// 성공 메시지 표시
  static void showSuccess(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.success,
      foregroundColor: AppColors.neutral0,
      icon: Icons.check_circle_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 에러 메시지 표시
  static void showError(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.error,
      foregroundColor: AppColors.neutral0,
      icon: Icons.error_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 정보 메시지 표시
  static void showInfo(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.info,
      foregroundColor: AppColors.neutral0,
      icon: Icons.info_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 경고 메시지 표시
  static void showWarning(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.warning,
      foregroundColor: AppColors.neutral100,
      icon: Icons.warning_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 일반 메시지 표시
  static void showMessage(
    BuildContext context,
    String message, {
    String? actionLabel,
    VoidCallback? onAction,
    Duration? duration,
    bool? dismissible,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.neutral80,
      foregroundColor: AppColors.neutral0,
      icon: Icons.message_rounded,
      actionLabel: actionLabel,
      onAction: onAction,
      duration: duration,
      dismissible: dismissible,
    );
  }

  /// 현재 표시 중인 토스트 메시지 해제
  static void dismiss(BuildContext context) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }

  /// 기존 호환성을 위한 일반 토스트 메서드
  static void showToast(
    BuildContext context,
    String message, {
    Duration? duration,
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: AppColors.neutral80,
      foregroundColor: AppColors.neutral0,
      icon: Icons.message_rounded,
      duration: duration,
    );
  }

  /// 기존 호환성을 위한 Duration 상수들
  static const Duration shortDuration = Duration(seconds: 2);
  static const Duration longDuration = Duration(seconds: 3);

  /// 기본 SnackBar 표시 (즉시 실행, 안전한 context 사용)
  static void _showSnackBar(
    BuildContext context,
    String message, {
    IconData? icon,
    Color? backgroundColor,
    Color? foregroundColor,
    Duration? duration,
    String? actionLabel,
    VoidCallback? onAction,
    bool? dismissible,
  }) {
    try {
      // context 유효성 체크
      if (!context.mounted) return;
      
      // SnackBar 콘텐츠 구성
      Widget content = Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: foregroundColor ?? Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: foregroundColor ?? Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      );

      // SnackBar 생성
      final snackBar = SnackBar(
        content: content,
        backgroundColor: backgroundColor ?? Colors.black87,
        duration: duration ?? const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        // 액션 버튼이 있는 경우
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: foregroundColor ?? Colors.white,
                onPressed: onAction,
              )
            : null,
      );

      // SnackBar 표시 (안전한 context 사용)
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      
    } catch (e) {
      // 에러가 발생해도 앱이 크래시되지 않도록 처리
      LoggerUtils.logError('ToastUtils._showSnackBar error: $e', tag: 'ToastUtils', error: e);
      
      // 최후의 폴백: 가장 기본적인 SnackBar
      try {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor ?? Colors.black87,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
        }
      } catch (fallbackError) {
        LoggerUtils.logError('ToastUtils fallback error: $fallbackError', tag: 'ToastUtils', error: fallbackError);
      }
    }
  }
}
