import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class QrScanScreen extends StatefulWidget {
  const QrScanScreen({Key? key}) : super(key: key);

  @override
  State<QrScanScreen> createState() => _QrScanScreenState();
}

class _QrScanScreenState extends State<QrScanScreen> {
  MobileScannerController controller = MobileScannerController();
  bool _flashOn = false;

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final media = MediaQuery.of(context);
    final isPortrait = media.orientation == Orientation.portrait;
    final scanSize = isPortrait
        ? media.size.width * 0.6
        : media.size.height * 0.6;
    final topOffset = isPortrait ? media.size.height * 0.25 : media.size.height * 0.12;
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          MobileScanner(
            controller: controller,
            fit: BoxFit.cover,
            onDetect: (capture) {
              final List<Barcode> barcodes = capture.barcodes;
              if (barcodes.isNotEmpty && barcodes.first.rawValue != null) {
                Navigator.of(context).pop(barcodes.first.rawValue);
              }
            },
          ),
          // 네 모서리만 흰색으로 표시 (상단에서 12% 위치에 고정)
          Positioned(
            top: topOffset,
            left: (media.size.width - scanSize) / 2,
            child: SizedBox(
              width: scanSize,
              height: scanSize,
              child: CustomPaint(
                painter: _CornerPainter(),
              ),
            ),
          ),
          // 안내문구: 사각형 바로 아래에 고정
          Positioned(
            top: topOffset + scanSize + 16,
            left: 0,
            right: 0,
            child: const Center(
              child: Text(
                'QR 코드를 찾는 중입니다.',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  shadows: [Shadow(blurRadius: 4, color: Colors.black, offset: Offset(0, 1))],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // 하단 플래시 버튼만 중앙에 배치
          Positioned(
            bottom: 48,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(_flashOn ? Icons.flash_on : Icons.flash_off, color: Colors.white, size: 32),
                  onPressed: () {
                    setState(() {
                      _flashOn = !_flashOn;
                      controller.toggleTorch();
                    });
                  },
                  tooltip: '플래시',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _CornerPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 5
      ..style = PaintingStyle.stroke;
    const length = 32.0;
    // 네 모서리 그리기
    // 좌상
    canvas.drawLine(Offset(0, 0), Offset(length, 0), paint);
    canvas.drawLine(Offset(0, 0), Offset(0, length), paint);
    // 우상
    canvas.drawLine(Offset(size.width, 0), Offset(size.width - length, 0), paint);
    canvas.drawLine(Offset(size.width, 0), Offset(size.width, length), paint);
    // 좌하
    canvas.drawLine(Offset(0, size.height), Offset(length, size.height), paint);
    canvas.drawLine(Offset(0, size.height), Offset(0, size.height - length), paint);
    // 우하
    canvas.drawLine(Offset(size.width, size.height), Offset(size.width - length, size.height), paint);
    canvas.drawLine(Offset(size.width, size.height), Offset(size.width, size.height - length), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 