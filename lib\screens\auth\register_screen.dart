import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'verify_email_screen.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/orientation_helper.dart';
import '../../widgets/onboarding_components.dart';

/// 회원가입 화면 - 웜톤 디자인으로 개선
///
/// 단계별 진행 표시와 개선된 폼 디자인을 적용한 현대적 회원가입 경험
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class RegisterScreen extends StatefulWidget {
  final VoidCallback onRegisterSuccess;
  final VoidCallback onLoginRequested;
  const RegisterScreen({super.key, required this.onRegisterSuccess, required this.onLoginRequested});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final phoneController = TextEditingController();
  // FocusNode 추가
  final FocusNode emailFocus = FocusNode();
  final FocusNode passwordFocus = FocusNode();
  final FocusNode confirmPasswordFocus = FocusNode();
  final FocusNode phoneFocus = FocusNode();
  // 닉네임 관련 변수/컨트롤러 완전 삭제
  bool isLoading = false;
  String? error;
  bool agreeTerms = false;
  bool passwordVisible = false;
  bool confirmPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();
  }
  final phoneMaskFormatter = MaskTextInputFormatter(
    mask: '###-####-####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    phoneController.dispose();
    emailFocus.dispose();
    passwordFocus.dispose();
    confirmPasswordFocus.dispose();
    phoneFocus.dispose();
    // nicknameController.dispose(); // 삭제
    super.dispose();
  }

  bool get isPasswordMatch => passwordController.text == confirmPasswordController.text;

  Future<void> _handleRegister() async {
    setState(() { isLoading = true; error = null; });
    if (emailController.text.trim().isEmpty) {
      setState(() { error = '이메일을 입력하세요.'; isLoading = false; });
      return;
    }
    if (passwordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호를 입력하세요.'; isLoading = false; });
      return;
    }
    if (confirmPasswordController.text.trim().isEmpty) {
      setState(() { error = '비밀번호 확인을 입력하세요.'; isLoading = false; });
      return;
    }
    if (!isPasswordMatch) {
      setState(() { error = '비밀번호가 일치하지 않습니다.'; isLoading = false; });
      return;
    }
    if (!agreeTerms) {
      setState(() { error = '약관에 동의해야 회원가입이 가능합니다.'; isLoading = false; });
      return;
    }
    try {
      final auth = FirebaseAuth.instance;
      final userCred = await auth.createUserWithEmailAndPassword(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );
      // Firestore에 정보 저장 (닉네임 관련 필드 완전 삭제)
      await FirebaseFirestore.instance.collection('users').doc(userCred.user!.uid).set({
        'phone': phoneController.text.trim(),
        'email': emailController.text.trim(),
        'createdAt': FieldValue.serverTimestamp(),
        'agreement': {
          'agreed': true,
          'agreedAt': FieldValue.serverTimestamp(),
        },
      }, SetOptions(merge: true));
      await userCred.user!.sendEmailVerification();

      // 이메일 인증 대기 상태 설정
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('email_verification_pending', true);

      await auth.signOut();
      // 이메일 인증 안내 페이지로 이동
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const VerifyEmailScreen(),
        ),
      );
    } on FirebaseAuthException catch (e) {
      setState(() { error = _firebaseErrorToKorean(e); });
    } catch (e) {
      setState(() { error = '알 수 없는 오류'; });
    } finally {
      setState(() { isLoading = false; });
    }
  }

  String _firebaseErrorToKorean(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-email':
        return '올바른 이메일 형식이 아닙니다.';
      case 'email-already-in-use':
        return '이미 가입된 이메일입니다.';
      case 'weak-password':
        return '비밀번호는 6자 이상이어야 합니다.';
      case 'too-many-requests':
        return '잠시 후 다시 시도해 주세요.';
      case 'network-request-failed':
        return '네트워크 오류가 발생했습니다.';
      default:
        return '입력 정보를 다시 확인해 주세요.';
    }
  }

  void _showTermsDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(child: Text(content)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }

  static const String termsOfService = '''[샘플] 서비스 이용약관\n\n제1조(목적) ... (이하 생략, 실제 서비스 오픈 전 교체 필요)''';
  static const String privacyPolicy = '''[샘플] 개인정보처리방침\n\n제1조(목적) ... (이하 생략, 실제 서비스 오픈 전 교체 필요)''';

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        widget.onLoginRequested();
      },
      child: Scaffold(
        body: OnboardingComponents.buildBackground(
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: OnboardingComponents.buildCard(
                  context: context,
                  child: _buildRegisterForm(context),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 회원가입 폼 구성
  Widget _buildRegisterForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 헤더
        _buildHeader(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 진행 단계 표시
        _buildProgressIndicator(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 이메일 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: emailController,
          focusNode: emailFocus,
          label: '이메일',
          hint: '<EMAIL>',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onSubmitted: (_) => FocusScope.of(context).requestFocus(passwordFocus),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 입력
        _buildPasswordField(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 비밀번호 확인 입력
        _buildConfirmPasswordField(context),

        // 비밀번호 불일치 메시지
        if (!isPasswordMatch && confirmPasswordController.text.isNotEmpty)
          _buildPasswordMismatchMessage(context),

        OnboardingComponents.buildSmallSpacing(context),

        // 핸드폰 번호 입력
        OnboardingComponents.buildTextField(
          context: context,
          controller: phoneController,
          focusNode: phoneFocus,
          label: '핸드폰번호 (선택)',
          hint: '010-0000-0000',
          prefixIcon: Icons.phone_outlined,
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.done,
          onSubmitted: (_) => _handleRegister(),
        ),

        OnboardingComponents.buildSectionSpacing(context),

        // 약관 동의
        _buildTermsAgreement(context),

        OnboardingComponents.buildSectionSpacing(context),

        // 에러 메시지
        if (error != null) _buildErrorMessage(context),

        // 회원가입 버튼
        OnboardingComponents.buildPrimaryButton(
          context: context,
          text: '회원가입',
          onPressed: _canRegister() ? _handleRegister : null,
          isLoading: isLoading,
          icon: Icons.person_add,
        ),

        OnboardingComponents.buildSectionSpacing(context),

        // 로그인 링크
        _buildLoginLink(context),
      ],
    );
  }
  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 로고 아이콘
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: OnboardingColors.primaryGradient,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            boxShadow: [
              BoxShadow(
                color: OnboardingColors.primary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.person_add_rounded,
            size: 30,
            color: OnboardingColors.textOnPrimary,
          ),
        ),

        OnboardingComponents.buildSmallSpacing(context),

        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '회원가입',
        ),

        const SizedBox(height: 8),

        // 부제목
        OnboardingComponents.buildBody(
          context: context,
          text: '파라바라와 함께 시작해보세요',
        ),
      ],
    );
  }

  /// 진행 단계 표시
  Widget _buildProgressIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: OnboardingColors.primaryOverlay,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
        border: Border.all(color: OnboardingColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: OnboardingColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '계정 정보를 입력하고 이메일 인증을 완료해주세요',
              style: TextStyle(
                color: OnboardingColors.primary,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 비밀번호 입력 필드
  Widget _buildPasswordField(BuildContext context) {
    return TextFormField(
      controller: passwordController,
      focusNode: passwordFocus,
      obscureText: !passwordVisible,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) => FocusScope.of(context).requestFocus(confirmPasswordFocus),
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        color: OnboardingColors.textPrimary,
      ),
      decoration: InputDecoration(
        labelText: '비밀번호',
        prefixIcon: Icon(
          Icons.lock_outline,
          color: OnboardingColors.primary,
          size: ResponsiveHelper.getIconSize(context),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            passwordVisible ? Icons.visibility : Icons.visibility_off,
            color: OnboardingColors.textSecondary,
          ),
          onPressed: () => setState(() => passwordVisible = !passwordVisible),
        ),
        labelStyle: TextStyle(
          color: OnboardingColors.textSecondary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
        ),
        filled: true,
        fillColor: OnboardingColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: OnboardingColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: OnboardingColors.error, width: 2),
        ),
        contentPadding: ResponsiveHelper.getButtonPadding(context),
      ),
    );
  }

  /// 비밀번호 확인 입력 필드
  Widget _buildConfirmPasswordField(BuildContext context) {
    return TextFormField(
      controller: confirmPasswordController,
      focusNode: confirmPasswordFocus,
      obscureText: !confirmPasswordVisible,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) => FocusScope.of(context).requestFocus(phoneFocus),
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context),
        color: OnboardingColors.textPrimary,
      ),
      decoration: InputDecoration(
        labelText: '비밀번호 확인',
        prefixIcon: Icon(
          Icons.lock_outline,
          color: OnboardingColors.primary,
          size: ResponsiveHelper.getIconSize(context),
        ),
        suffixIcon: IconButton(
          icon: Icon(
            confirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
            color: OnboardingColors.textSecondary,
          ),
          onPressed: () => setState(() => confirmPasswordVisible = !confirmPasswordVisible),
        ),
        labelStyle: TextStyle(
          color: OnboardingColors.textSecondary,
          fontSize: ResponsiveHelper.getBodyFontSize(context),
        ),
        filled: true,
        fillColor: OnboardingColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: OnboardingColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          borderSide: BorderSide(color: OnboardingColors.error, width: 2),
        ),
        contentPadding: ResponsiveHelper.getButtonPadding(context),
      ),
    );
  }

  /// 비밀번호 불일치 메시지
  Widget _buildPasswordMismatchMessage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: OnboardingColors.errorLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: OnboardingColors.error.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: OnboardingColors.error, size: 16),
            const SizedBox(width: 8),
            Text(
              '비밀번호가 일치하지 않습니다.',
              style: TextStyle(
                color: OnboardingColors.error,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 약관 동의 섹션
  Widget _buildTermsAgreement(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 전체 동의 체크박스
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: OnboardingColors.surfaceVariant,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
          ),
          child: Row(
            children: [
              Checkbox(
                value: agreeTerms,
                onChanged: (v) => setState(() => agreeTerms = v ?? false),
                activeColor: OnboardingColors.primary,
              ),
              Expanded(
                child: Text(
                  '아래 약관 모두에 동의합니다.',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getBodyFontSize(context),
                    color: OnboardingColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // 약관 링크들
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              GestureDetector(
                onTap: () => _showTermsDialog('서비스 이용약관', termsOfService),
                child: Text(
                  '서비스 이용약관',
                  style: TextStyle(
                    color: OnboardingColors.primary,
                    fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => _showTermsDialog('개인정보처리방침', privacyPolicy),
                child: Text(
                  '개인정보처리방침',
                  style: TextStyle(
                    color: OnboardingColors.primary,
                    fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 에러 메시지
  Widget _buildErrorMessage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: OnboardingColors.errorLight.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: OnboardingColors.error.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: OnboardingColors.error, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                error!,
                style: TextStyle(
                  color: OnboardingColors.error,
                  fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 로그인 링크
  Widget _buildLoginLink(BuildContext context) {
    return TextButton(
      onPressed: widget.onLoginRequested,
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: ResponsiveHelper.getBodyFontSize(context),
            color: OnboardingColors.textSecondary,
          ),
          children: [
            const TextSpan(text: '이미 계정이 있으신가요? '),
            TextSpan(
              text: '로그인',
              style: TextStyle(
                color: OnboardingColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 회원가입 가능 여부 확인
  bool _canRegister() {
    return !isLoading &&
           isPasswordMatch &&
           passwordController.text.isNotEmpty &&
           confirmPasswordController.text.isNotEmpty &&
           agreeTerms;
  }
}