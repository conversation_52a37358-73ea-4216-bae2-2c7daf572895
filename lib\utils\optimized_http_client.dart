import 'dart:convert';
import 'package:http/http.dart' as http;
import 'network_optimizer.dart';
import 'logger_utils.dart';

/// 최적화된 HTTP 클라이언트
/// 
/// NetworkOptimizer를 활용하여 네트워크 요청을 최적화합니다.
/// - 캐싱
/// - 배치 처리
/// - 중복 요청 방지
/// - 압축
class OptimizedHttpClient {
  final NetworkOptimizer _networkOptimizer;
  final http.Client _httpClient;

  OptimizedHttpClient({
    NetworkOptimizerConfig? config,
  }) : _networkOptimizer = NetworkOptimizer(config: config),
       _httpClient = http.Client();

  /// 초기화
  Future<void> initialize() async {
    await _networkOptimizer.initialize();
    LoggerUtils.logInfo('OptimizedHttpClient 초기화 완료', tag: 'OptimizedHttpClient');
  }

  /// GET 요청
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Duration? cacheDuration,
    CachePolicy cachePolicy = CachePolicy.networkFirst,
  }) async {
    final requestConfig = NetworkRequestConfig.get(url).copyWith(
      headers: headers,
      cacheDuration: cacheDuration ?? const Duration(minutes: 5),
      cachePolicy: cachePolicy,
    );

    final response = await _networkOptimizer.executeRequest(requestConfig);
    return _convertToHttpResponse(response);
  }

  /// POST 요청
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration? cacheDuration,
    CachePolicy cachePolicy = CachePolicy.noCache,
  }) async {
    final requestConfig = NetworkRequestConfig.post(url, body: body).copyWith(
      headers: headers,
      cacheDuration: cacheDuration ?? const Duration(minutes: 5),
      cachePolicy: cachePolicy,
    );

    final response = await _networkOptimizer.executeRequest(requestConfig);
    return _convertToHttpResponse(response);
  }

  /// PUT 요청
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration? cacheDuration,
    CachePolicy cachePolicy = CachePolicy.noCache,
  }) async {
    final requestConfig = NetworkRequestConfig(
      type: RequestType.put,
      url: url,
      headers: headers,
      body: body,
      cacheDuration: cacheDuration ?? const Duration(minutes: 5),
      cachePolicy: cachePolicy,
    );

    final response = await _networkOptimizer.executeRequest(requestConfig);
    return _convertToHttpResponse(response);
  }

  /// DELETE 요청
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration? cacheDuration,
    CachePolicy cachePolicy = CachePolicy.noCache,
  }) async {
    final requestConfig = NetworkRequestConfig(
      type: RequestType.delete,
      url: url,
      headers: headers,
      body: body,
      cacheDuration: cacheDuration ?? const Duration(minutes: 5),
      cachePolicy: cachePolicy,
    );

    final response = await _networkOptimizer.executeRequest(requestConfig);
    return _convertToHttpResponse(response);
  }

  /// PATCH 요청
  Future<http.Response> patch(
    String url, {
    Map<String, String>? headers,
    Object? body,
    Duration? cacheDuration,
    CachePolicy cachePolicy = CachePolicy.noCache,
  }) async {
    final requestConfig = NetworkRequestConfig(
      type: RequestType.patch,
      url: url,
      headers: headers,
      body: body,
      cacheDuration: cacheDuration ?? const Duration(minutes: 5),
      cachePolicy: cachePolicy,
    );

    final response = await _networkOptimizer.executeRequest(requestConfig);
    return _convertToHttpResponse(response);
  }

  /// NetworkResponse를 http.Response로 변환
  http.Response _convertToHttpResponse(NetworkResponse networkResponse) {
    return http.Response(
      jsonEncode(networkResponse.data),
      networkResponse.statusCode,
      headers: networkResponse.headers,
    );
  }

  /// 캐시 정리
  void clearCache() {
    // NetworkOptimizer의 캐시 정리 메서드 호출
    LoggerUtils.logInfo('캐시 정리 완료', tag: 'OptimizedHttpClient');
  }

  /// 통계 정보 조회
  Map<String, dynamic> getStats() {
    return {
      'network_optimizer_initialized': true,
      'client_initialized': true,
    };
  }

  /// 리소스 정리
  void close() {
    _httpClient.close();
    LoggerUtils.logInfo('OptimizedHttpClient 정리 완료', tag: 'OptimizedHttpClient');
  }
} 